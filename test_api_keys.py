#!/usr/bin/env python3
"""
TradingAgents-CN API密钥测试脚本
测试所有配置的API密钥是否正常工作
"""

import os
import sys
import traceback
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

# 使用统一的环境变量加载器
from tradingagents.utils.env_loader import env_loader, get_api_key

def test_dashscope_api():
    """测试阿里云通义千问API"""
    print("🇨🇳 测试阿里云通义千问 (DashScope) API...")
    
    try:
        api_key = get_api_key('dashscope')
        if not api_key:
            print("⚠️  DashScope API key 未配置")
            return False
            
        import dashscope
        dashscope.api_key = api_key
        
        # 简单测试调用
        from dashscope import Generation
        
        response = Generation.call(
            model='qwen-turbo',
            prompt='你好，请简单介绍一下你自己。',
            max_tokens=50
        )
        
        if response.status_code == 200:
            print("✅ DashScope API 测试成功")
            print(f"   响应: {response.output.text[:100]}...")
            return True
        else:
            print(f"❌ DashScope API 测试失败: {response.message}")
            return False
            
    except Exception as e:
        print(f"❌ DashScope API 测试失败: {e}")
        return False

def test_deepseek_api():
    """测试DeepSeek API"""
    print("\n🚀 测试DeepSeek API...")
    
    try:
        api_key = get_api_key('deepseek')
        base_url = env_loader.get_config_value('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')

        if not api_key:
            print("⚠️  DeepSeek API key 未配置")
            return False
            
        from openai import OpenAI
        
        client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "user", "content": "你好，请简单介绍一下你自己。"}
            ],
            max_tokens=50
        )
        
        print("✅ DeepSeek API 测试成功")
        print(f"   响应: {response.choices[0].message.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek API 测试失败: {e}")
        return False

def test_openrouter_api():
    """测试OpenRouter API"""
    print("\n🌐 测试OpenRouter API...")
    
    try:
        api_key = get_api_key('openrouter')

        if not api_key:
            print("⚠️  OpenRouter API key 未配置")
            return False
            
        from openai import OpenAI
        
        client = OpenAI(
            api_key=api_key,
            base_url="https://openrouter.ai/api/v1"
        )
        
        # 使用免费模型测试
        response = client.chat.completions.create(
            model="meta-llama/llama-3.2-3b-instruct:free",
            messages=[
                {"role": "user", "content": "Hello, please introduce yourself briefly."}
            ],
            max_tokens=50
        )
        
        print("✅ OpenRouter API 测试成功")
        print(f"   响应: {response.choices[0].message.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ OpenRouter API 测试失败: {e}")
        return False

def test_google_api():
    """测试Google Gemini API"""
    print("\n🔍 测试Google Gemini API...")
    
    try:
        api_key = get_api_key('google')

        if not api_key:
            print("⚠️  Google API key 未配置")
            return False
            
        import google.generativeai as genai
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro')
        
        response = model.generate_content("Hello, please introduce yourself briefly.")
        
        print("✅ Google Gemini API 测试成功")
        print(f"   响应: {response.text[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Google Gemini API 测试失败: {e}")
        return False

def test_tushare_api():
    """测试Tushare API"""
    print("\n📈 测试Tushare API...")
    
    try:
        token = get_api_key('tushare')

        if not token:
            print("⚠️  Tushare token 未配置")
            return False
            
        import tushare as ts
        
        ts.set_token(token)
        pro = ts.pro_api()
        
        # 获取股票基本信息
        df = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
        
        if len(df) > 0:
            print("✅ Tushare API 测试成功")
            print(f"   获取到 {len(df)} 只股票信息")
            return True
        else:
            print("❌ Tushare API 测试失败: 未获取到数据")
            return False
            
    except Exception as e:
        print(f"❌ Tushare API 测试失败: {e}")
        return False

def test_finnhub_api():
    """测试FinnHub API"""
    print("\n📊 测试FinnHub API...")
    
    try:
        api_key = get_api_key('finnhub')

        if not api_key:
            print("⚠️  FinnHub API key 未配置")
            return False
            
        import finnhub
        
        finnhub_client = finnhub.Client(api_key=api_key)
        
        # 获取苹果股票的基本信息
        profile = finnhub_client.company_profile2(symbol='AAPL')
        
        if profile and 'name' in profile:
            print("✅ FinnHub API 测试成功")
            print(f"   获取到公司信息: {profile['name']}")
            return True
        else:
            print("❌ FinnHub API 测试失败: 未获取到数据")
            return False
            
    except Exception as e:
        print(f"❌ FinnHub API 测试失败: {e}")
        return False

def test_reddit_api():
    """测试Reddit API"""
    print("\n🔴 测试Reddit API...")
    
    try:
        client_id = env_loader.get_config_value('REDDIT_CLIENT_ID')
        client_secret = env_loader.get_config_value('REDDIT_CLIENT_SECRET')
        user_agent = env_loader.get_config_value('REDDIT_USER_AGENT', 'TradingAgents-CN/1.0')

        if not client_id or not client_secret:
            print("⚠️  Reddit API 配置不完整")
            return False
            
        import praw
        
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # 测试获取热门帖子
        subreddit = reddit.subreddit('stocks')
        hot_posts = list(subreddit.hot(limit=1))
        
        if hot_posts:
            print("✅ Reddit API 测试成功")
            print(f"   获取到帖子: {hot_posts[0].title[:50]}...")
            return True
        else:
            print("❌ Reddit API 测试失败: 未获取到数据")
            return False
            
    except Exception as e:
        print(f"❌ Reddit API 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔑 TradingAgents-CN API密钥测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    results = {}
    
    # 测试所有API
    results['DashScope'] = test_dashscope_api()
    results['DeepSeek'] = test_deepseek_api()
    results['OpenRouter'] = test_openrouter_api()
    results['Google'] = test_google_api()
    results['Tushare'] = test_tushare_api()
    results['FinnHub'] = test_finnhub_api()
    results['Reddit'] = test_reddit_api()
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📋 测试结果汇总")
    print("=" * 60)
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    for service, success in results.items():
        status = "✅ 正常" if success else "❌ 失败"
        print(f"{service:12} : {status}")
    
    print("-" * 60)
    print(f"总计: {success_count}/{total_count} 个服务正常工作")
    
    if success_count > 0:
        print("\n🎉 恭喜！至少有一个API服务正常工作，您可以开始使用TradingAgents-CN了！")
        
        # 推荐配置
        if results.get('DashScope'):
            print("💡 推荐使用DashScope (通义千问) 作为主要AI模型")
        elif results.get('DeepSeek'):
            print("💡 推荐使用DeepSeek作为主要AI模型")
        elif results.get('OpenRouter'):
            print("💡 推荐使用OpenRouter访问多种AI模型")
            
        if results.get('Tushare'):
            print("💡 推荐使用Tushare获取中国股市数据")
        if results.get('FinnHub'):
            print("💡 推荐使用FinnHub获取美股数据")
            
    else:
        print("\n⚠️  没有API服务正常工作，请检查您的API密钥配置")
        print("💡 建议：")
        print("   1. 检查.env文件中的API密钥是否正确")
        print("   2. 确认网络连接正常")
        print("   3. 验证API密钥是否有效且有足够额度")
    
    print("\n📖 更多帮助请查看 QUICKSTART_CN.md 文件")

if __name__ == "__main__":
    main()
