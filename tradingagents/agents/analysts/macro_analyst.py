from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.tools import BaseTool
from langchain_core.messages import HumanMessage
import traceback

# 导入统一日志系统
from tradingagents.utils.logging_init import get_logger
logger = get_logger("default")

# 导入装饰器
from tradingagents.agents.utils.decorators import log_analyst_module


def create_macro_analyst(llm, toolkit):
    @log_analyst_module("macro")
    def macro_analyst_node(state):
        logger.debug(f"🌍 [DEBUG] ===== 宏观经济分析师节点开始 =====")

        current_date = state["trade_date"]
        ticker = state["company_of_interest"]

        logger.debug(f"🌍 [DEBUG] 输入参数: ticker={ticker}, date={current_date}")
        logger.debug(f"🌍 [DEBUG] 当前状态中的消息数量: {len(state.get('messages', []))}")
        logger.debug(f"🌍 [DEBUG] 现有宏观报告: {state.get('macro_report', 'None')}")

        # 根据股票代码格式选择数据源
        from tradingagents.utils.stock_utils import StockUtils

        market_info = StockUtils.get_market_info(ticker)
        is_china = market_info['is_china']
        is_hk = market_info['is_hk']
        is_us = market_info['is_us']

        logger.debug(f"🌍 [DEBUG] 股票类型检查: {ticker} -> {market_info['market_name']} ({market_info['currency_name']})")

        # 获取公司名称和行业信息
        def _get_company_info(ticker, market_info):
            """获取公司名称和行业信息"""
            try:
                if market_info['is_china']:
                    # 中国A股，尝试从基本面报告获取
                    fundamentals_report = state.get('fundamentals_report', '')
                    if fundamentals_report and '公司名称' in fundamentals_report:
                        import re
                        name_match = re.search(r'公司名称[：:]\s*([^\n\r，,。.]+)', fundamentals_report)
                        industry_match = re.search(r'所属行业[：:]\s*([^\n\r，,。.]+)', fundamentals_report)
                        
                        company_name = name_match.group(1).strip() if name_match else ticker
                        industry = industry_match.group(1).strip() if industry_match else "未知行业"
                        
                        return company_name, industry
                
                # 默认返回股票代码
                return ticker, "未知行业"
            except Exception as e:
                logger.warning(f"⚠️ 获取公司信息失败: {e}")
                return ticker, "未知行业"

        company_name, industry = _get_company_info(ticker, market_info)
        logger.debug(f"🌍 [DEBUG] 公司信息: {ticker} -> {company_name} ({industry})")

        # 选择工具
        if toolkit.config["online_tools"]:
            # 在线模式：使用宏观经济数据工具
            if is_china:
                # 中国宏观经济分析工具
                class ChinaMacroDataTool(BaseTool):
                    name: str = "get_china_macro_data"
                    description: str = f"获取中国宏观经济数据，包括GDP、CPI、PMI、货币政策等，用于分析对{ticker}的影响。直接调用，无需参数。"

                    def _run(self, query: str = "") -> str:
                        try:
                            logger.debug(f"🌍 [DEBUG] ChinaMacroDataTool调用")
                            # 获取中国宏观经济新闻和数据
                            macro_news = toolkit.get_global_news_openai.invoke({
                                'query': f'中国宏观经济 GDP CPI PMI 货币政策 央行 {current_date}',
                                'max_results': 10
                            })
                            
                            # 获取行业相关的宏观数据
                            industry_news = toolkit.get_global_news_openai.invoke({
                                'query': f'中国 {industry} 行业政策 监管 发展趋势 {current_date}',
                                'max_results': 5
                            })
                            
                            return f"=== 中国宏观经济数据 ===\n{macro_news}\n\n=== {industry}行业宏观环境 ===\n{industry_news}"
                        except Exception as e:
                            return f"获取中国宏观数据失败: {str(e)}"

                class ChinaPolicyDataTool(BaseTool):
                    name: str = "get_china_policy_data"
                    description: str = f"获取中国政策动态和监管变化，分析对{ticker}所在{industry}行业的影响。直接调用，无需参数。"

                    def _run(self, query: str = "") -> str:
                        try:
                            logger.debug(f"🌍 [DEBUG] ChinaPolicyDataTool调用")
                            # 获取政策相关新闻
                            policy_news = toolkit.get_google_news.invoke({
                                'query': f'中国政策 {industry} 监管 法规 {company_name} {current_date}',
                                'max_results': 8
                            })
                            
                            return f"=== 中国政策和监管动态 ===\n{policy_news}"
                        except Exception as e:
                            return f"获取政策数据失败: {str(e)}"

                tools = [ChinaMacroDataTool(), ChinaPolicyDataTool()]
                query = f"""请对中国A股{ticker}（{company_name}）进行详细的宏观经济分析。

执行步骤：
1. 使用get_china_macro_data工具获取中国宏观经济数据和趋势
2. 使用get_china_policy_data工具获取相关政策和监管动态
3. 基于获取的真实数据分析宏观环境对该股票的影响
4. 直接输出完整的宏观经济分析报告内容

重要要求：
- 必须输出完整的宏观经济分析报告内容，不要只是描述报告已完成
- 报告必须基于工具获取的真实数据进行分析
- 报告长度不少于1000字
- 重点分析宏观因素对{company_name}的具体影响
- 关注{industry}行业的宏观环境变化

报告格式应包含：
## 中国宏观经济环境分析
## 货币政策和财政政策影响
## 行业政策和监管环境
## 国际经济环境影响
## 宏观风险因素识别
## 基于宏观分析的投资建议"""

            else:
                # 美股/港股宏观经济分析工具
                class GlobalMacroDataTool(BaseTool):
                    name: str = "get_global_macro_data"
                    description: str = f"获取全球宏观经济数据，包括美联储政策、全球经济指标等，用于分析对{ticker}的影响。直接调用，无需参数。"

                    def _run(self, query: str = "") -> str:
                        try:
                            logger.debug(f"🌍 [DEBUG] GlobalMacroDataTool调用")
                            # 获取全球宏观经济新闻
                            if is_hk:
                                macro_query = f'香港经济 美联储 利率 汇率 {industry} {current_date}'
                            else:
                                macro_query = f'美国经济 美联储 利率 通胀 GDP {industry} {current_date}'
                            
                            macro_news = toolkit.get_global_news_openai.invoke({
                                'query': macro_query,
                                'max_results': 10
                            })
                            
                            # 获取国际贸易和地缘政治影响
                            geopolitical_news = toolkit.get_global_news_openai.invoke({
                                'query': f'国际贸易 地缘政治 {industry} 全球供应链 {current_date}',
                                'max_results': 5
                            })
                            
                            return f"=== 全球宏观经济数据 ===\n{macro_news}\n\n=== 地缘政治和贸易环境 ===\n{geopolitical_news}"
                        except Exception as e:
                            return f"获取全球宏观数据失败: {str(e)}"

                class USPolicyDataTool(BaseTool):
                    name: str = "get_us_policy_data"
                    description: str = f"获取美国/国际政策动态，分析对{ticker}所在{industry}行业的影响。直接调用，无需参数。"

                    def _run(self, query: str = "") -> str:
                        try:
                            logger.debug(f"🌍 [DEBUG] USPolicyDataTool调用")
                            # 获取政策相关新闻
                            policy_query = f'美国政策 监管 {industry} SEC FDA {company_name} {current_date}' if is_us else f'香港政策 监管 {industry} {company_name} {current_date}'
                            
                            policy_news = toolkit.get_google_news.invoke({
                                'query': policy_query,
                                'max_results': 8
                            })
                            
                            return f"=== {'美国' if is_us else '香港'}政策和监管动态 ===\n{policy_news}"
                        except Exception as e:
                            return f"获取政策数据失败: {str(e)}"

                tools = [GlobalMacroDataTool(), USPolicyDataTool()]
                market_name = "港股" if is_hk else "美股"
                query = f"""请对{market_name}{ticker}（{company_name}）进行详细的宏观经济分析。

执行步骤：
1. 使用get_global_macro_data工具获取全球宏观经济数据和趋势
2. 使用get_us_policy_data工具获取相关政策和监管动态
3. 基于获取的真实数据分析宏观环境对该股票的影响
4. 直接输出完整的宏观经济分析报告内容

重要要求：
- 必须输出完整的宏观经济分析报告内容，不要只是描述报告已完成
- 报告必须基于工具获取的真实数据进行分析
- 报告长度不少于1000字
- 重点分析宏观因素对{company_name}的具体影响
- 关注{industry}行业的宏观环境变化

报告格式应包含：
## 全球宏观经济环境分析
## 美联储政策和利率环境影响
## 行业政策和监管环境
## 国际贸易和地缘政治影响
## 汇率和大宗商品价格影响
## 基于宏观分析的投资建议"""

            # 使用ReAct Agent执行宏观分析
            try:
                from langchain import hub
                from langchain.agents import create_react_agent, AgentExecutor

                # 创建ReAct Agent
                prompt = hub.pull("hwchase17/react")
                agent = create_react_agent(llm, tools, prompt)
                agent_executor = AgentExecutor(
                    agent=agent,
                    tools=tools,
                    verbose=True,
                    handle_parsing_errors=True,
                    max_iterations=10,  # 宏观分析需要多次数据获取
                    max_execution_time=200  # 3分钟多，给足够时间获取多源数据
                )

                logger.debug(f"🌍 [DEBUG] 执行ReAct Agent宏观分析...")
                result = agent_executor.invoke({'input': query})

                report = result['output']
                logger.info(f"🌍 [宏观经济分析师] ReAct Agent完成，报告长度: {len(report)}")

            except Exception as e:
                logger.error(f"❌ [DEBUG] ReAct Agent失败: {str(e)}")
                # 生成降级分析报告
                market_name = "中国A股" if is_china else ("港股" if is_hk else "美股")
                report = f"""# 🌍 宏观经济分析报告 - {company_name}（{ticker}）

## 📋 分析概述
由于技术原因，无法执行完整的宏观数据获取，但基于{market_name}市场特点提供以下宏观分析框架：

## 🏛️ 宏观经济环境分析框架
**货币政策影响**
- 关注央行利率政策变化对{industry}行业的影响
- 分析流动性环境对股票估值的影响
- 观察汇率波动对公司业务的潜在影响

**财政政策分析**
- 评估政府支出政策对{industry}行业的支持力度
- 分析税收政策变化对公司盈利的影响
- 关注产业政策对行业发展的指导作用

## 📊 行业宏观环境
**{industry}行业政策环境**
- 监管政策变化趋势分析
- 行业准入门槛和竞争格局变化
- 技术创新政策对行业发展的推动作用

**市场环境分析**
- 上下游产业链政策影响
- 国际贸易环境对行业的影响
- 消费政策对需求端的影响

## 🌐 国际环境影响
**地缘政治风险**
- 国际关系变化对{market_name}市场的影响
- 贸易政策调整对相关行业的影响
- 全球供应链重构的机遇与挑战

## ⚠️ 风险提示
- 本报告因技术限制未能获取最新宏观数据
- 建议关注官方政策发布和经济数据更新
- 宏观环境变化具有不确定性，需持续跟踪

## 📝 技术说明
系统遇到技术问题：{str(e)}
建议检查网络连接和数据源配置后重新分析。

---
*报告生成时间：{current_date}*
*分析师：宏观经济分析师（降级模式）*"""
        else:
            # 离线模式，使用原有逻辑
            report = "离线模式，暂不支持宏观经济分析"

        return {
            "messages": [HumanMessage(content=report)],
            "macro_report": report,
        }

    return macro_analyst_node
