from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.tools import BaseTool
from langchain_core.messages import HumanMessage
import traceback

# 导入统一日志系统
from tradingagents.utils.logging_init import get_logger
logger = get_logger("default")

# 导入装饰器
from tradingagents.agents.utils.decorators import log_analyst_module


def create_quantitative_analyst(llm, toolkit):
    @log_analyst_module("quantitative")
    def quantitative_analyst_node(state):
        logger.debug(f"📊 [DEBUG] ===== 量化分析师节点开始 =====")

        current_date = state["trade_date"]
        ticker = state["company_of_interest"]

        logger.debug(f"📊 [DEBUG] 输入参数: ticker={ticker}, date={current_date}")
        logger.debug(f"📊 [DEBUG] 当前状态中的消息数量: {len(state.get('messages', []))}")
        logger.debug(f"📊 [DEBUG] 现有量化报告: {state.get('quantitative_report', 'None')}")

        # 根据股票代码格式选择数据源
        from tradingagents.utils.stock_utils import StockUtils

        market_info = StockUtils.get_market_info(ticker)
        is_china = market_info['is_china']
        is_hk = market_info['is_hk']
        is_us = market_info['is_us']

        logger.debug(f"📊 [DEBUG] 股票类型检查: {ticker} -> {market_info['market_name']} ({market_info['currency_name']})")

        # 获取公司名称
        def _get_company_name(ticker, market_info):
            """获取公司名称"""
            try:
                if market_info['is_china']:
                    # 中国A股，尝试从基本面报告获取
                    fundamentals_report = state.get('fundamentals_report', '')
                    if fundamentals_report and '公司名称' in fundamentals_report:
                        import re
                        match = re.search(r'公司名称[：:]\s*([^\n\r，,。.]+)', fundamentals_report)
                        if match:
                            return match.group(1).strip()
                
                # 默认返回股票代码
                return ticker
            except Exception as e:
                logger.warning(f"⚠️ 获取公司名称失败: {e}")
                return ticker

        company_name = _get_company_name(ticker, market_info)
        logger.debug(f"📊 [DEBUG] 公司名称: {ticker} -> {company_name}")

        # 选择工具
        if toolkit.config["online_tools"]:
            # 在线模式：使用统一数据接口和量化分析工具
            if is_china:
                # 中国A股量化分析工具
                class ChinaQuantDataTool(BaseTool):
                    name: str = "get_china_quant_data"
                    description: str = f"获取中国A股股票{ticker}的量化分析数据，包括技术指标、统计指标、波动率等。直接调用，无需参数。"

                    def _run(self, query: str = "") -> str:
                        try:
                            logger.debug(f"📊 [DEBUG] ChinaQuantDataTool调用，股票代码: {ticker}")
                            # 使用优化的缓存数据获取
                            from tradingagents.dataflows.optimized_china_data import get_china_stock_data_cached
                            return get_china_stock_data_cached(
                                symbol=ticker,
                                start_date='2024-01-01',  # 量化分析需要更长的历史数据
                                end_date=current_date,
                                force_refresh=False
                            )
                        except Exception as e:
                            logger.error(f"❌ 优化A股量化数据获取失败: {e}")
                            # 备用方案：使用原始API
                            try:
                                return toolkit.get_china_stock_data.invoke({
                                    'stock_code': ticker,
                                    'start_date': '2024-01-01',
                                    'end_date': current_date
                                })
                            except Exception as e2:
                                return f"获取量化数据失败: {str(e2)}"

                class ChinaFundamentalsDataTool(BaseTool):
                    name: str = "get_china_fundamentals_data"
                    description: str = f"获取中国A股股票{ticker}的基本面数据，用于量化估值分析。直接调用，无需参数。"

                    def _run(self, query: str = "") -> str:
                        try:
                            logger.debug(f"📊 [DEBUG] ChinaFundamentalsDataTool调用，股票代码: {ticker}")
                            return toolkit.get_china_fundamentals.invoke({
                                'stock_code': ticker,
                                'start_date': '2024-01-01',
                                'end_date': current_date
                            })
                        except Exception as e:
                            return f"获取基本面数据失败: {str(e)}"

                tools = [ChinaQuantDataTool(), ChinaFundamentalsDataTool()]
                query = f"""请对中国A股{ticker}进行详细的量化分析。

执行步骤：
1. 使用get_china_quant_data工具获取股票的历史价格和技术指标数据
2. 使用get_china_fundamentals_data工具获取基本面数据用于量化估值
3. 基于获取的真实数据进行深入的量化分析
4. 直接输出完整的量化分析报告内容

重要要求：
- 必须输出完整的量化分析报告内容，不要只是描述报告已完成
- 报告必须基于工具获取的真实数据进行分析
- 报告长度不少于1000字
- 包含具体的数学模型、统计指标和量化策略
- 使用人民币（¥）作为价格单位

报告格式应包含：
## 股票基本信息
## 量化技术指标分析
## 统计特征分析
## 波动率和风险分析
## 量化估值模型
## 算法交易策略建议
## 量化投资建议"""

            else:
                # 美股/港股量化分析工具
                class USQuantDataTool(BaseTool):
                    name: str = "get_us_quant_data"
                    description: str = f"获取美股/港股{ticker}的量化分析数据，包括技术指标、统计指标、波动率等。直接调用，无需参数。"

                    def _run(self, query: str = "") -> str:
                        try:
                            logger.debug(f"📊 [DEBUG] USQuantDataTool调用，股票代码: {ticker}")
                            # 使用优化的缓存数据获取
                            from tradingagents.dataflows.optimized_us_data import get_us_stock_data_cached
                            return get_us_stock_data_cached(
                                symbol=ticker,
                                start_date='2024-01-01',  # 量化分析需要更长的历史数据
                                end_date=current_date,
                                force_refresh=False
                            )
                        except Exception as e:
                            logger.error(f"❌ 优化美股量化数据获取失败: {e}")
                            # 备用方案：使用原始API
                            try:
                                return toolkit.get_YFin_data_online.invoke({
                                    'symbol': ticker,
                                    'start_date': '2024-01-01',
                                    'end_date': current_date
                                })
                            except Exception as e2:
                                return f"获取量化数据失败: {str(e2)}"

                class USFundamentalsDataTool(BaseTool):
                    name: str = "get_us_fundamentals_data"
                    description: str = f"获取美股/港股{ticker}的基本面数据，用于量化估值分析。直接调用，无需参数。"

                    def _run(self, query: str = "") -> str:
                        try:
                            logger.debug(f"📊 [DEBUG] USFundamentalsDataTool调用，股票代码: {ticker}")
                            return toolkit.get_stock_fundamentals_unified.invoke({
                                'ticker': ticker,
                                'start_date': '2024-01-01',
                                'end_date': current_date,
                                'curr_date': current_date
                            })
                        except Exception as e:
                            return f"获取基本面数据失败: {str(e)}"

                tools = [USQuantDataTool(), USFundamentalsDataTool()]
                query = f"""请对{'港股' if is_hk else '美股'}{ticker}进行详细的量化分析。

执行步骤：
1. 使用get_us_quant_data工具获取股票的历史价格和技术指标数据
2. 使用get_us_fundamentals_data工具获取基本面数据用于量化估值
3. 基于获取的真实数据进行深入的量化分析
4. 直接输出完整的量化分析报告内容

重要要求：
- 必须输出完整的量化分析报告内容，不要只是描述报告已完成
- 报告必须基于工具获取的真实数据进行分析
- 报告长度不少于1000字
- 包含具体的数学模型、统计指标和量化策略
- 使用美元（$）作为价格单位

报告格式应包含：
## 股票基本信息
## 量化技术指标分析
## 统计特征分析
## 波动率和风险分析
## 量化估值模型
## 算法交易策略建议
## 量化投资建议"""

            # 使用ReAct Agent执行量化分析
            try:
                from langchain import hub
                from langchain.agents import create_react_agent, AgentExecutor

                # 创建ReAct Agent
                prompt = hub.pull("hwchase17/react")
                agent = create_react_agent(llm, tools, prompt)
                agent_executor = AgentExecutor(
                    agent=agent,
                    tools=tools,
                    verbose=True,
                    handle_parsing_errors=True,
                    max_iterations=12,  # 量化分析需要更多迭代
                    max_execution_time=240  # 4分钟，给更多时间进行复杂计算
                )

                logger.debug(f"📊 [DEBUG] 执行ReAct Agent量化分析...")
                result = agent_executor.invoke({'input': query})

                report = result['output']
                logger.info(f"📊 [量化分析师] ReAct Agent完成，报告长度: {len(report)}")

            except Exception as e:
                logger.error(f"❌ [DEBUG] ReAct Agent失败: {str(e)}")
                report = f"ReAct Agent量化分析失败: {str(e)}"
        else:
            # 离线模式，使用原有逻辑
            report = "离线模式，暂不支持量化分析"

        return {
            "messages": [HumanMessage(content=report)],
            "quantitative_report": report,
        }

    return quantitative_analyst_node
