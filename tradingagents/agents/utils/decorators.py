"""
装饰器模块 - 为分析师提供通用的装饰器功能
"""

import functools
import time
from typing import Callable, Any

# 导入统一日志系统
from tradingagents.utils.logging_init import get_logger
logger = get_logger("default")


def log_analyst_module(analyst_type: str):
    """
    分析师模块日志装饰器
    
    Args:
        analyst_type: 分析师类型 (如: "market", "fundamentals", "quantitative", "macro")
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            
            # 获取状态信息用于日志
            state = args[0] if args else {}
            ticker = state.get("company_of_interest", "Unknown") if isinstance(state, dict) else "Unknown"
            
            logger.info(f"🚀 [{analyst_type.upper()}] 分析师开始执行: {ticker}")
            logger.debug(f"📊 [{analyst_type.upper()}] 输入参数: args={len(args)}, kwargs={list(kwargs.keys())}")
            
            try:
                # 执行原函数
                result = func(*args, **kwargs)
                
                # 计算执行时间
                execution_time = time.time() - start_time
                
                # 记录成功日志
                if isinstance(result, dict):
                    report_key = f"{analyst_type}_report"
                    report_length = len(result.get(report_key, "")) if report_key in result else 0
                    logger.info(f"✅ [{analyst_type.upper()}] 分析完成: {ticker}, 报告长度: {report_length}, 耗时: {execution_time:.2f}s")
                else:
                    logger.info(f"✅ [{analyst_type.upper()}] 分析完成: {ticker}, 耗时: {execution_time:.2f}s")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"❌ [{analyst_type.upper()}] 分析失败: {ticker}, 错误: {str(e)}, 耗时: {execution_time:.2f}s")
                
                # 返回错误状态
                return {
                    "messages": [],
                    f"{analyst_type}_report": f"{analyst_type}分析失败: {str(e)}"
                }
        
        return wrapper
    return decorator


def timing_decorator(func: Callable) -> Callable:
    """
    简单的计时装饰器
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        logger.debug(f"⏱️ {func.__name__} 执行时间: {execution_time:.2f}s")
        return result
    return wrapper


def error_handler(default_return=None):
    """
    错误处理装饰器
    
    Args:
        default_return: 发生错误时的默认返回值
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"❌ {func.__name__} 执行失败: {str(e)}")
                return default_return
        return wrapper
    return decorator


def validate_state(required_fields: list):
    """
    状态验证装饰器
    
    Args:
        required_fields: 必需的状态字段列表
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 假设第一个参数是state
            if args and isinstance(args[0], dict):
                state = args[0]
                missing_fields = [field for field in required_fields if field not in state]
                if missing_fields:
                    logger.warning(f"⚠️ {func.__name__} 缺少必需字段: {missing_fields}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator
