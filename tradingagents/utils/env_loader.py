#!/usr/bin/env python3
"""
统一的环境变量加载工具
确保所有模块都能正确加载.env文件中的API密钥
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional, Any
import logging

# 设置日志
logger = logging.getLogger(__name__)

class EnvLoader:
    """环境变量加载器"""
    
    _instance = None
    _loaded = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._loaded:
            self._load_env_file()
            self._loaded = True
    
    def _load_env_file(self):
        """加载.env文件"""
        try:
            from dotenv import load_dotenv
            
            # 查找项目根目录
            current_dir = Path(__file__).parent
            project_root = None
            
            # 向上查找包含.env文件的目录
            for parent in [current_dir] + list(current_dir.parents):
                env_file = parent / ".env"
                if env_file.exists():
                    project_root = parent
                    break
            
            if project_root:
                env_file = project_root / ".env"
                load_dotenv(env_file, override=True)
                logger.info(f"✅ 已加载环境变量文件: {env_file}")
            else:
                logger.warning("⚠️ 未找到.env文件")
                
        except ImportError:
            logger.warning("⚠️ python-dotenv未安装，无法加载.env文件")
        except Exception as e:
            logger.error(f"❌ 加载.env文件失败: {e}")
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """获取指定提供商的API密钥"""
        key_mapping = {
            'dashscope': 'DASHSCOPE_API_KEY',
            'openai': 'OPENAI_API_KEY',
            'google': 'GOOGLE_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY',
            'deepseek': 'DEEPSEEK_API_KEY',
            'openrouter': 'OPENROUTER_API_KEY',
            'finnhub': 'FINNHUB_API_KEY',
            'tushare': 'TUSHARE_TOKEN',
        }
        
        env_key = key_mapping.get(provider.lower())
        if env_key:
            value = os.getenv(env_key)
            if value and value != f'your_{env_key.lower()}_here':
                return value
        return None
    
    def get_all_api_keys(self) -> Dict[str, Optional[str]]:
        """获取所有API密钥状态"""
        providers = ['dashscope', 'openai', 'google', 'anthropic', 'deepseek', 
                    'openrouter', 'finnhub', 'tushare']
        return {provider: self.get_api_key(provider) for provider in providers}
    
    def is_api_key_configured(self, provider: str) -> bool:
        """检查指定提供商的API密钥是否已配置"""
        return self.get_api_key(provider) is not None
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return os.getenv(key, default)
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            'mongodb': {
                'enabled': os.getenv('MONGODB_ENABLED', 'false').lower() == 'true',
                'host': os.getenv('MONGODB_HOST', 'localhost'),
                'port': int(os.getenv('MONGODB_PORT', '27017')),
                'username': os.getenv('MONGODB_USERNAME'),
                'password': os.getenv('MONGODB_PASSWORD'),
                'database': os.getenv('MONGODB_DATABASE', 'tradingagents'),
                'auth_source': os.getenv('MONGODB_AUTH_SOURCE', 'admin'),
            },
            'redis': {
                'enabled': os.getenv('REDIS_ENABLED', 'false').lower() == 'true',
                'host': os.getenv('REDIS_HOST', 'localhost'),
                'port': int(os.getenv('REDIS_PORT', '6379')),
                'password': os.getenv('REDIS_PASSWORD'),
                'db': int(os.getenv('REDIS_DB', '0')),
            }
        }
    
    def validate_required_keys(self, required_providers: list) -> Dict[str, bool]:
        """验证必需的API密钥是否已配置"""
        results = {}
        for provider in required_providers:
            results[provider] = self.is_api_key_configured(provider)
        return results
    
    def print_status(self):
        """打印环境变量配置状态"""
        print("🔑 环境变量配置状态")
        print("=" * 50)
        
        api_keys = self.get_all_api_keys()
        for provider, key in api_keys.items():
            status = "✅ 已配置" if key else "❌ 未配置"
            print(f"  {provider.upper()}: {status}")
        
        db_config = self.get_database_config()
        print(f"\n🗄️ 数据库配置:")
        print(f"  MongoDB: {'✅ 启用' if db_config['mongodb']['enabled'] else '❌ 禁用'}")
        print(f"  Redis: {'✅ 启用' if db_config['redis']['enabled'] else '❌ 禁用'}")

# 创建全局实例
env_loader = EnvLoader()

# 便捷函数
def get_api_key(provider: str) -> Optional[str]:
    """获取API密钥的便捷函数"""
    return env_loader.get_api_key(provider)

def is_api_key_configured(provider: str) -> bool:
    """检查API密钥是否配置的便捷函数"""
    return env_loader.is_api_key_configured(provider)

def get_config_value(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return env_loader.get_config_value(key, default)

def ensure_env_loaded():
    """确保环境变量已加载"""
    global env_loader
    if not env_loader._loaded:
        env_loader._load_env_file()

if __name__ == "__main__":
    # 测试脚本
    env_loader.print_status()
