#!/bin/bash
# TradingAgents-CN 一键启动脚本 (macOS/Linux)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONDA_ENV="tradingagents-cn"
WEB_PORT=8501
LOG_FILE="$PROJECT_DIR/logs/launcher.log"

# 创建日志目录
mkdir -p "$PROJECT_DIR/logs"

# 日志函数
log_message() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" >> "$LOG_FILE"
    echo -e "$message"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🚀 TradingAgents-CN                      ║"
    echo "║                     智能交易分析系统                          ║"
    echo "║                      一键启动程序                            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 检查依赖
check_dependencies() {
    log_message "${BLUE}🔍 检查系统依赖...${NC}"
    
    # 检查conda
    if ! command -v conda &> /dev/null; then
        log_message "${RED}❌ 未找到conda，请先安装Anaconda或Miniconda${NC}"
        return 1
    fi
    
    # 检查conda环境
    if ! conda env list | grep -q "$CONDA_ENV"; then
        log_message "${RED}❌ Conda环境 '$CONDA_ENV' 不存在${NC}"
        log_message "${YELLOW}💡 请运行以下命令创建环境:${NC}"
        log_message "   conda create -n $CONDA_ENV python=3.11"
        return 1
    fi
    
    # 检查关键文件
    local key_files=("start_web.py" ".env" "tradingagents/__init__.py")
    for file in "${key_files[@]}"; do
        if [[ ! -f "$PROJECT_DIR/$file" ]]; then
            log_message "${RED}❌ 关键文件缺失: $file${NC}"
            return 1
        fi
    done
    
    log_message "${GREEN}✅ 依赖检查通过${NC}"
    return 0
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$WEB_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "${YELLOW}⚠️  端口 $WEB_PORT 已被占用${NC}"
        log_message "${YELLOW}正在尝试停止占用进程...${NC}"
        
        # 尝试停止streamlit进程
        pkill -f "streamlit.*start_web.py" 2>/dev/null
        sleep 2
        
        if lsof -Pi :$WEB_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_message "${RED}❌ 无法释放端口 $WEB_PORT${NC}"
            return 1
        else
            log_message "${GREEN}✅ 端口已释放${NC}"
        fi
    fi
    return 0
}

# 启动服务
start_services() {
    log_message "${BLUE}🚀 启动 TradingAgents-CN 服务...${NC}"
    
    # 切换到项目目录
    cd "$PROJECT_DIR" || {
        log_message "${RED}❌ 无法切换到项目目录${NC}"
        return 1
    }
    
    # 获取conda路径
    local conda_base=$(conda info --base)
    
    # 启动streamlit
    log_message "${BLUE}🔧 启动Streamlit Web服务...${NC}"
    
    # 在后台启动streamlit
    source "$conda_base/etc/profile.d/conda.sh"
    conda activate "$CONDA_ENV"
    
    # 启动streamlit并将输出重定向到日志
    nohup streamlit run start_web.py \
        --server.port $WEB_PORT \
        --server.address 0.0.0.0 \
        --server.headless true \
        --server.runOnSave false \
        >> "$LOG_FILE" 2>&1 &
    
    local streamlit_pid=$!
    echo $streamlit_pid > "$PROJECT_DIR/.streamlit_pid"
    
    # 等待服务启动
    log_message "${YELLOW}⏳ 等待服务启动...${NC}"
    local max_attempts=30
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        if curl -s "http://localhost:$WEB_PORT" >/dev/null 2>&1; then
            log_message "${GREEN}✅ Streamlit服务启动成功!${NC}"
            log_message "${CYAN}🌐 Web界面地址: http://localhost:$WEB_PORT${NC}"
            return 0
        fi
        
        # 检查进程是否还在运行
        if ! kill -0 $streamlit_pid 2>/dev/null; then
            log_message "${RED}❌ Streamlit进程意外退出${NC}"
            return 1
        fi
        
        sleep 1
        ((attempt++))
        
        # 显示进度
        if [[ $((attempt % 5)) -eq 0 ]]; then
            log_message "${YELLOW}⏳ 等待中... ($attempt/$max_attempts)${NC}"
        fi
    done
    
    log_message "${RED}❌ 服务启动超时${NC}"
    return 1
}

# 打开浏览器
open_browser() {
    log_message "${BLUE}🔗 正在打开浏览器...${NC}"
    
    # 等待2秒确保服务完全启动
    sleep 2
    
    # 根据系统打开浏览器
    if command -v open &> /dev/null; then
        # macOS
        open "http://localhost:$WEB_PORT"
    elif command -v xdg-open &> /dev/null; then
        # Linux
        xdg-open "http://localhost:$WEB_PORT"
    else
        log_message "${YELLOW}⚠️  无法自动打开浏览器，请手动访问: http://localhost:$WEB_PORT${NC}"
    fi
}

# 停止服务
stop_services() {
    log_message "${BLUE}🛑 正在停止服务...${NC}"
    
    # 读取PID文件
    local pid_file="$PROJECT_DIR/.streamlit_pid"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            log_message "${GREEN}✅ 服务已停止 (PID: $pid)${NC}"
        fi
        rm -f "$pid_file"
    fi
    
    # 额外清理
    pkill -f "streamlit.*start_web.py" 2>/dev/null
    
    log_message "${GREEN}✅ 清理完成${NC}"
}

# 显示状态
show_status() {
    echo -e "${PURPLE}📊 系统状态:${NC}"
    echo "  项目目录: $PROJECT_DIR"
    echo "  Conda环境: $CONDA_ENV"
    echo "  Web端口: $WEB_PORT"
    echo "  日志文件: $LOG_FILE"
    echo
    
    # 检查服务状态
    if curl -s "http://localhost:$WEB_PORT" >/dev/null 2>&1; then
        echo -e "  服务状态: ${GREEN}✅ 运行中${NC}"
        echo -e "  访问地址: ${CYAN}http://localhost:$WEB_PORT${NC}"
    else
        echo -e "  服务状态: ${RED}❌ 未运行${NC}"
    fi
    echo
}

# 运行测试
run_tests() {
    log_message "${BLUE}🧪 运行系统测试...${NC}"
    
    cd "$PROJECT_DIR" || return 1
    
    local conda_base=$(conda info --base)
    source "$conda_base/etc/profile.d/conda.sh"
    conda activate "$CONDA_ENV"
    
    if python test_system.py; then
        log_message "${GREEN}✅ 系统测试通过${NC}"
    else
        log_message "${RED}❌ 系统测试失败${NC}"
        return 1
    fi
}

# 显示帮助
show_help() {
    echo -e "${CYAN}TradingAgents-CN 一键启动脚本${NC}"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  start     启动服务 (默认)"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    显示状态"
    echo "  test      运行测试"
    echo "  logs      查看日志"
    echo "  help      显示帮助"
    echo
}

# 查看日志
show_logs() {
    if [[ -f "$LOG_FILE" ]]; then
        echo -e "${BLUE}📋 最近的日志 (最后50行):${NC}"
        echo "----------------------------------------"
        tail -50 "$LOG_FILE"
        echo "----------------------------------------"
        echo -e "${YELLOW}完整日志文件: $LOG_FILE${NC}"
    else
        echo -e "${YELLOW}⚠️  日志文件不存在${NC}"
    fi
}

# 主函数
main() {
    local action="${1:-start}"
    
    case "$action" in
        "start")
            show_banner
            if check_dependencies && check_port && start_services; then
                open_browser
                echo
                log_message "${GREEN}🎉 TradingAgents-CN 启动成功!${NC}"
                log_message "${CYAN}🌐 Web界面: http://localhost:$WEB_PORT${NC}"
                log_message "${YELLOW}💡 使用 '$0 stop' 停止服务${NC}"
                log_message "${YELLOW}💡 使用 '$0 logs' 查看日志${NC}"
            else
                log_message "${RED}❌ 启动失败，请查看日志: $LOG_FILE${NC}"
                exit 1
            fi
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 2
            main "start"
            ;;
        "status")
            show_status
            ;;
        "test")
            run_tests
            ;;
        "logs")
            show_logs
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}❌ 未知选项: $action${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 信号处理
trap 'stop_services; exit 0' INT TERM

# 运行主函数
main "$@"
