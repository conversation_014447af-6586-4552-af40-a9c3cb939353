# TradingAgents-CN 快速入门指南

## 🎉 恭喜！系统已成功安装

您的 TradingAgents-CN 系统已经成功安装并通过了基本测试。以下是快速开始使用的步骤。

## 📋 系统状态

### ✅ 已安装并测试通过的组件

- **数据源**
  - ✅ AKShare (中国股市数据) - 已测试，可获取实时数据
  - ✅ yfinance (国际股市数据) - 已安装，可能有频率限制
  - ✅ Tushare (专业金融数据) - 已安装，需要配置token

- **AI模型**
  - ✅ OpenAI GPT - 已安装，需要配置API key
  - ✅ Anthropic Claude - 已安装，需要配置API key
  - ✅ LangChain - 已安装并可用
  - ✅ Google Gemini - 已安装，需要配置API key
  - ✅ 阿里云通义千问 - 已安装，需要配置API key

- **数据库**
  - ✅ ChromaDB (向量数据库) - 已安装并可用
  - ⚠️ Redis (缓存) - 已安装，需要启动服务
  - ⚠️ MongoDB (数据存储) - 已安装，需要启动服务

- **Web界面**
  - ✅ Streamlit - 已启动，访问 http://localhost:8501
  - ✅ Chainlit - 已安装并可用

- **交易工具**
  - ✅ Backtrader (回测框架) - 已安装并可用
  - ✅ StockStats (技术指标) - 已安装并可用

## 🚀 立即开始

### 1. 配置API密钥

编辑 `.env` 文件，添加您的API密钥：

```bash
# OpenAI (推荐先配置这个)
OPENAI_API_KEY=sk-your-openai-api-key-here

# 其他可选配置
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key
DASHSCOPE_API_KEY=your-dashscope-api-key
TUSHARE_TOKEN=your-tushare-token
```

### 2. 启动Web界面

Web界面已经在运行中，访问：
- **Streamlit界面**: http://localhost:8501

如果需要重新启动：
```bash
conda activate tradingagents-cn
streamlit run start_web.py
```

### 3. 测试基本功能

运行测试脚本确认一切正常：
```bash
conda activate tradingagents-cn
python test_system.py
```

### 4. 获取股票数据示例

```python
import akshare as ak
import pandas as pd

# 获取上证指数数据
data = ak.stock_zh_index_daily(symbol='sh000001')
print(data.tail())

# 获取个股数据 (以平安银行为例)
stock_data = ak.stock_zh_a_daily(symbol='000001')
print(stock_data.tail())
```

## 📚 主要功能

### 1. 数据获取
- 中国A股实时数据 (AKShare)
- 国际股市数据 (yfinance)
- 新闻和社交媒体情感分析
- 技术指标计算

### 2. AI分析
- 智能投资建议
- 市场趋势分析
- 风险评估
- 自动化交易策略

### 3. 回测系统
- 历史数据回测
- 策略性能评估
- 风险指标计算
- 可视化报告

### 4. Web界面
- 直观的数据展示
- 交互式图表
- 实时监控面板
- 策略配置界面

## 🔧 可选配置

### 启动数据库服务 (可选)

如果需要使用缓存和数据持久化功能：

```bash
# 启动Redis (macOS)
brew services start redis

# 启动MongoDB (macOS)
brew services start mongodb-community
```

### 配置更多数据源

1. **Tushare专业数据**
   - 注册账号：https://tushare.pro/
   - 获取token并添加到 `.env` 文件

2. **其他API密钥**
   - Alpha Vantage: https://www.alphavantage.co/
   - Financial Modeling Prep: https://financialmodelingprep.com/
   - EODHD: https://eodhistoricaldata.com/

## 📖 下一步

1. **浏览示例**: 查看 `examples/` 目录中的示例代码
2. **阅读文档**: 查看 `docs/` 目录中的详细文档
3. **运行测试**: 执行 `tests/` 目录中的测试用例
4. **自定义策略**: 在 `tradingagents/strategies/` 中创建您的交易策略

## 🆘 需要帮助？

- 查看 `README.md` 获取详细信息
- 检查 `docs/` 目录中的文档
- 运行 `python test_system.py` 诊断问题
- 查看日志文件 `logs/tradingagents.log`

## 🎯 快速示例

创建一个简单的股票分析脚本：

```python
# quick_example.py
import akshare as ak
import pandas as pd
from datetime import datetime, timedelta

# 获取最近30天的上证指数数据
end_date = datetime.now()
start_date = end_date - timedelta(days=30)

# 获取数据
data = ak.stock_zh_index_daily(symbol='sh000001')
recent_data = data.tail(30)

# 简单分析
print(f"最新收盘价: {recent_data.iloc[-1]['close']}")
print(f"30天最高价: {recent_data['high'].max()}")
print(f"30天最低价: {recent_data['low'].min()}")
print(f"30天平均价: {recent_data['close'].mean():.2f}")

# 计算简单移动平均线
recent_data['MA5'] = recent_data['close'].rolling(window=5).mean()
recent_data['MA10'] = recent_data['close'].rolling(window=10).mean()

print("\n最近5天数据:")
print(recent_data[['date', 'close', 'MA5', 'MA10']].tail())
```

运行示例：
```bash
conda activate tradingagents-cn
python quick_example.py
```

---

🎉 **恭喜您成功安装了 TradingAgents-CN！开始您的智能交易之旅吧！**
