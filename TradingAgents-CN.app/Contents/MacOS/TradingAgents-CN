#!/bin/bash
# TradingAgents-CN macOS应用程序启动脚本

# 获取应用程序路径
APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
PROJECT_DIR="/Users/<USER>/Desktop/TradingAgents-CN-main"

# 设置环境变量
export PYTHONPATH="$PROJECT_DIR:$PYTHONPATH"

# 日志文件
LOG_FILE="$PROJECT_DIR/logs/app_launcher.log"
mkdir -p "$(dirname "$LOG_FILE")"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理
handle_error() {
    log_message "❌ 发生错误: $1"
    osascript -e "display dialog \"启动失败: $1\" buttons {\"确定\"} default button \"确定\" with icon stop"
    exit 1
}

# 检查项目目录
if [[ ! -d "$PROJECT_DIR" ]]; then
    handle_error "项目目录不存在: $PROJECT_DIR"
fi

# 切换到项目目录
cd "$PROJECT_DIR" || handle_error "无法切换到项目目录"

log_message "🚀 启动TradingAgents-CN应用程序"
log_message "📁 项目目录: $PROJECT_DIR"

# 检查conda
if ! command -v conda &> /dev/null; then
    handle_error "未找到conda，请先安装Anaconda或Miniconda"
fi

# 检查conda环境
if ! conda env list | grep -q "tradingagents-cn"; then
    handle_error "Conda环境 'tradingagents-cn' 不存在"
fi

# 显示启动通知
osascript -e "display notification \"正在启动TradingAgents-CN...\" with title \"TradingAgents-CN\" sound name \"Glass\""

# 启动Python GUI启动器
log_message "🔧 启动图形界面..."

# 获取conda路径并启动
CONDA_BASE=$(conda info --base)
source "$CONDA_BASE/etc/profile.d/conda.sh"
conda activate tradingagents-cn

# 启动GUI启动器
python "$PROJECT_DIR/launch_tradingagents.py" --gui 2>&1 | tee -a "$LOG_FILE"

log_message "✅ 应用程序已退出"
