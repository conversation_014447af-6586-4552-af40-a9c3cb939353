# TradingAgents-CN 进一步部署指南

## 🎯 当前状态
✅ 基础系统已部署完成  
✅ 核心功能正常工作  
✅ Web界面已启动  

## 🚀 进一步部署步骤

### 第一阶段：完善核心配置

#### 1. 启用数据库服务（推荐）

**启动Redis缓存服务**
```bash
# macOS用户
brew install redis
brew services start redis

# 验证Redis运行
redis-cli ping
# 应该返回: PONG
```

**启动MongoDB数据库**
```bash
# macOS用户
brew install mongodb-community
brew services start mongodb-community

# 验证MongoDB运行
mongosh --eval "db.runCommand('ping')"
# 应该返回: { ok: 1 }
```

**更新.env配置**
```bash
# 编辑.env文件，启用数据库
MONGODB_ENABLED=true
REDIS_ENABLED=true
```

#### 2. 完善API密钥配置

**测试并配置剩余API**
```bash
# 运行完整API测试
conda activate tradingagents-cn
python test_api_keys.py
```

**推荐配置优先级**：
1. **必需**: DashScope (通义千问) - 已配置 ✅
2. **推荐**: DeepSeek V3 - 已配置 ✅  
3. **可选**: Google Gemini - 需要测试
4. **数据**: Tushare专业版 - 需要升级积分

### 第二阶段：增强功能部署

#### 3. 部署高级数据源

**配置专业数据源**
```bash
# 1. 升级Tushare积分（推荐）
# 访问: https://tushare.pro/document/1?doc_id=13
# 获得更高API调用频率和更多数据

# 2. 配置Alpha Vantage（可选）
# 获取免费API: https://www.alphavantage.co/support/#api-key
# 添加到.env: ALPHA_VANTAGE_API_KEY=your_key_here

# 3. 配置Financial Modeling Prep（可选）
# 获取API: https://financialmodelingprep.com/developer/docs
# 添加到.env: FMP_API_KEY=your_key_here
```

#### 4. 部署实时数据流

**启用实时数据推送**
```bash
# 创建实时数据配置
cat > config/realtime_config.yaml << EOF
realtime:
  enabled: true
  sources:
    - akshare
    - tushare
  update_interval: 60  # 秒
  symbols:
    - "000001.SZ"  # 平安银行
    - "000002.SZ"  # 万科A
    - "600000.SH"  # 浦发银行
EOF
```

#### 5. 配置智能策略引擎

**部署预置策略**
```bash
# 创建策略目录
mkdir -p strategies/custom

# 复制示例策略
cp examples/strategies/* strategies/custom/

# 配置策略参数
cat > config/strategy_config.yaml << EOF
strategies:
  enabled:
    - "ma_crossover"
    - "rsi_reversal"
    - "bollinger_bands"
  risk_management:
    max_position_size: 0.1
    stop_loss: 0.05
    take_profit: 0.15
EOF
```

### 第三阶段：生产环境部署

#### 6. Docker容器化部署（推荐生产环境）

**构建Docker镜像**
```bash
# 构建镜像
docker build -t tradingagents-cn:latest .

# 启动完整服务栈
docker-compose up -d

# 验证服务状态
docker-compose ps
```

**Docker服务包括**：
- Web应用 (端口8501)
- Redis缓存 (端口6379)
- MongoDB数据库 (端口27017)
- Redis管理界面 (端口8081)
- MongoDB管理界面 (端口8082)

#### 7. 配置反向代理（生产环境）

**Nginx配置**
```bash
# 安装Nginx
brew install nginx

# 配置文件
cat > /usr/local/etc/nginx/servers/tradingagents.conf << EOF
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8501;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# 启动Nginx
brew services start nginx
```

#### 8. 配置SSL证书（生产环境）

```bash
# 使用Let's Encrypt免费证书
brew install certbot

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
echo "0 12 * * * /usr/local/bin/certbot renew --quiet" | crontab -
```

### 第四阶段：监控和维护

#### 9. 部署监控系统

**系统监控**
```bash
# 安装监控工具
pip install psutil prometheus-client grafana-api

# 启动监控服务
python scripts/start_monitoring.py
```

**日志管理**
```bash
# 配置日志轮转
cat > /etc/logrotate.d/tradingagents << EOF
/path/to/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 user group
}
EOF
```

#### 10. 配置自动备份

```bash
# 创建备份脚本
cat > scripts/backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/tradingagents"

# 备份数据库
mongodump --out \$BACKUP_DIR/mongodb_\$DATE
redis-cli --rdb \$BACKUP_DIR/redis_\$DATE.rdb

# 备份配置文件
tar -czf \$BACKUP_DIR/config_\$DATE.tar.gz config/ .env

# 清理旧备份（保留30天）
find \$BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

chmod +x scripts/backup.sh

# 添加到定时任务
echo "0 2 * * * /path/to/scripts/backup.sh" | crontab -
```

## 🎯 部署验证清单

### 基础功能验证
- [ ] Web界面正常访问
- [ ] AI模型响应正常
- [ ] 股票数据获取正常
- [ ] 数据库连接正常

### 高级功能验证
- [ ] 实时数据推送工作
- [ ] 策略引擎运行正常
- [ ] 监控系统正常
- [ ] 备份系统正常

### 性能验证
- [ ] 响应时间 < 3秒
- [ ] 内存使用 < 2GB
- [ ] CPU使用 < 50%
- [ ] 磁盘空间充足

## 🚨 故障排除

### 常见问题
1. **API调用失败**: 检查网络连接和API密钥
2. **数据库连接失败**: 确认服务已启动
3. **内存不足**: 调整Docker内存限制
4. **端口冲突**: 修改docker-compose.yml端口配置

### 日志查看
```bash
# 应用日志
tail -f logs/tradingagents.log

# Docker日志
docker-compose logs -f

# 系统资源
htop
```

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件
2. 运行诊断脚本: `python test_system.py`
3. 检查配置文件
4. 重启相关服务

---

**下一步建议**: 根据您的具体需求选择相应的部署阶段。建议先完成第一阶段，确保核心功能稳定后再进行后续部署。
