#!/usr/bin/env python3
"""
为TradingAgents-CN创建macOS应用程序包
生成可双击启动的.app应用程序
"""

import os
import shutil
from pathlib import Path
import subprocess

class MacOSAppCreator:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.app_name = "TradingAgents-CN"
        self.app_path = self.project_root / f"{self.app_name}.app"
        
    def create_app_structure(self):
        """创建.app目录结构"""
        print("🏗️  创建应用程序结构...")
        
        # 创建.app目录结构
        contents_dir = self.app_path / "Contents"
        macos_dir = contents_dir / "MacOS"
        resources_dir = contents_dir / "Resources"
        
        # 清理旧的应用程序
        if self.app_path.exists():
            shutil.rmtree(self.app_path)
        
        # 创建目录
        macos_dir.mkdir(parents=True, exist_ok=True)
        resources_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"✅ 应用程序结构已创建: {self.app_path}")
        return contents_dir, macos_dir, resources_dir
    
    def create_info_plist(self, contents_dir):
        """创建Info.plist文件"""
        print("📄 创建Info.plist...")
        
        info_plist_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>{self.app_name}</string>
    <key>CFBundleIdentifier</key>
    <string>com.tradingagents.cn</string>
    <key>CFBundleName</key>
    <string>{self.app_name}</string>
    <key>CFBundleDisplayName</key>
    <string>TradingAgents-CN 智能交易系统</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>TACN</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSUIElement</key>
    <false/>
    <key>CFBundleDocumentTypes</key>
    <array/>
</dict>
</plist>'''
        
        info_plist_path = contents_dir / "Info.plist"
        with open(info_plist_path, 'w') as f:
            f.write(info_plist_content)
        
        print("✅ Info.plist已创建")
    
    def create_executable_script(self, macos_dir):
        """创建可执行脚本"""
        print("🔧 创建可执行脚本...")
        
        script_content = f'''#!/bin/bash
# TradingAgents-CN macOS应用程序启动脚本

# 获取应用程序路径
APP_DIR="$(cd "$(dirname "${{BASH_SOURCE[0]}}")/../.." && pwd)"
PROJECT_DIR="{self.project_root}"

# 设置环境变量
export PYTHONPATH="$PROJECT_DIR:$PYTHONPATH"

# 日志文件
LOG_FILE="$PROJECT_DIR/logs/app_launcher.log"
mkdir -p "$(dirname "$LOG_FILE")"

# 日志函数
log_message() {{
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}}

# 错误处理
handle_error() {{
    log_message "❌ 发生错误: $1"
    osascript -e "display dialog \\"启动失败: $1\\" buttons {{\\"确定\\"}} default button \\"确定\\" with icon stop"
    exit 1
}}

# 检查项目目录
if [[ ! -d "$PROJECT_DIR" ]]; then
    handle_error "项目目录不存在: $PROJECT_DIR"
fi

# 切换到项目目录
cd "$PROJECT_DIR" || handle_error "无法切换到项目目录"

log_message "🚀 启动TradingAgents-CN应用程序"
log_message "📁 项目目录: $PROJECT_DIR"

# 检查conda
if ! command -v conda &> /dev/null; then
    handle_error "未找到conda，请先安装Anaconda或Miniconda"
fi

# 检查conda环境
if ! conda env list | grep -q "tradingagents-cn"; then
    handle_error "Conda环境 'tradingagents-cn' 不存在"
fi

# 显示启动通知
osascript -e "display notification \\"正在启动TradingAgents-CN...\\" with title \\"TradingAgents-CN\\" sound name \\"Glass\\""

# 启动Python GUI启动器
log_message "🔧 启动图形界面..."

# 获取conda路径并启动
CONDA_BASE=$(conda info --base)
source "$CONDA_BASE/etc/profile.d/conda.sh"
conda activate tradingagents-cn

# 启动GUI启动器
python "$PROJECT_DIR/launch_tradingagents.py" --gui 2>&1 | tee -a "$LOG_FILE"

log_message "✅ 应用程序已退出"
'''
        
        script_path = macos_dir / self.app_name
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # 设置执行权限
        script_path.chmod(0o755)
        
        print("✅ 可执行脚本已创建")
    
    def create_icon(self, resources_dir):
        """创建应用程序图标"""
        print("🎨 创建应用程序图标...")
        
        try:
            # 创建一个简单的图标（使用系统图标）
            icon_script = '''
            tell application "System Events"
                set iconPath to (path to desktop as string) & "temp_icon.icns"
                -- 这里可以添加自定义图标创建逻辑
            end tell
            '''
            
            # 如果有自定义图标文件，可以复制到Resources目录
            # 这里我们创建一个占位符
            icon_path = resources_dir / "app_icon.icns"
            
            # 尝试从系统复制一个合适的图标
            system_icon = "/System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/ExecutableBinaryIcon.icns"
            if Path(system_icon).exists():
                shutil.copy2(system_icon, icon_path)
                print("✅ 系统图标已复制")
            else:
                print("⚠️  未找到系统图标，跳过图标设置")
                
        except Exception as e:
            print(f"⚠️  图标创建失败: {e}")
    
    def create_desktop_alias(self):
        """在桌面创建应用程序别名"""
        print("🔗 创建桌面快捷方式...")
        
        try:
            desktop_path = Path.home() / "Desktop"
            alias_path = desktop_path / f"{self.app_name}.app"
            
            # 如果桌面已有同名文件，先删除
            if alias_path.exists():
                if alias_path.is_symlink():
                    alias_path.unlink()
                else:
                    shutil.rmtree(alias_path)
            
            # 创建符号链接
            alias_path.symlink_to(self.app_path)
            print(f"✅ 桌面快捷方式已创建: {alias_path}")
            
        except Exception as e:
            print(f"⚠️  桌面快捷方式创建失败: {e}")
    
    def create_launch_services_entry(self):
        """注册到Launch Services"""
        print("📋 注册应用程序...")
        
        try:
            # 使用lsregister注册应用程序
            subprocess.run([
                "/System/Library/Frameworks/CoreServices.framework/Frameworks/LaunchServices.framework/Support/lsregister",
                "-f", str(self.app_path)
            ], check=True, capture_output=True)
            
            print("✅ 应用程序已注册到系统")
            
        except subprocess.CalledProcessError as e:
            print(f"⚠️  应用程序注册失败: {e}")
        except FileNotFoundError:
            print("⚠️  lsregister工具未找到，跳过注册")
    
    def create_app(self):
        """创建完整的macOS应用程序"""
        print(f"🍎 开始创建macOS应用程序: {self.app_name}")
        print("=" * 60)
        
        try:
            # 创建应用程序结构
            contents_dir, macos_dir, resources_dir = self.create_app_structure()
            
            # 创建Info.plist
            self.create_info_plist(contents_dir)
            
            # 创建可执行脚本
            self.create_executable_script(macos_dir)
            
            # 创建图标
            self.create_icon(resources_dir)
            
            # 创建桌面快捷方式
            self.create_desktop_alias()
            
            # 注册应用程序
            self.create_launch_services_entry()
            
            print("\n" + "=" * 60)
            print("🎉 macOS应用程序创建成功!")
            print("=" * 60)
            print(f"📱 应用程序位置: {self.app_path}")
            print(f"🖥️  桌面快捷方式: ~/Desktop/{self.app_name}.app")
            print("\n🚀 使用方法:")
            print("1. 双击桌面上的应用程序图标")
            print("2. 或者在Finder中双击应用程序")
            print("3. 或者在Launchpad中找到应用程序")
            print("\n💡 提示:")
            print("- 首次运行可能需要在系统偏好设置中允许运行")
            print("- 应用程序会自动启动图形界面")
            print("- 可以通过右键菜单添加到Dock")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 应用程序创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    creator = MacOSAppCreator()
    success = creator.create_app()
    
    if success:
        print(f"\n🎊 恭喜！TradingAgents-CN macOS应用程序已成功创建！")
        print("现在您可以像使用其他macOS应用程序一样启动TradingAgents-CN了！")
    else:
        print("\n😞 应用程序创建失败，请检查错误信息并重试。")

if __name__ == "__main__":
    main()
