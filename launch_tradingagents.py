#!/usr/bin/env python3
"""
TradingAgents-CN 一键启动程序
支持图形界面和命令行两种模式
"""

import os
import sys
import time
import subprocess
import threading
import webbrowser
from pathlib import Path
from datetime import datetime

# 尝试导入GUI库
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

class TradingAgentsLauncher:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.conda_env = "tradingagents-cn"
        self.web_port = 8501
        self.processes = []
        self.is_running = False
        
    def log_message(self, message, widget=None):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        
        if widget:
            widget.insert(tk.END, log_msg + "\n")
            widget.see(tk.END)
            widget.update()
    
    def check_conda_env(self):
        """检查Conda环境是否存在"""
        try:
            result = subprocess.run(
                ["conda", "env", "list"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            return self.conda_env in result.stdout
        except:
            return False
    
    def check_dependencies(self):
        """检查系统依赖"""
        issues = []
        
        # 检查Conda
        if not self.check_conda_env():
            issues.append(f"Conda环境 '{self.conda_env}' 不存在")
        
        # 检查关键文件
        key_files = [
            "start_web.py",
            ".env",
            "tradingagents/__init__.py"
        ]
        
        for file in key_files:
            if not (self.project_root / file).exists():
                issues.append(f"关键文件缺失: {file}")
        
        return issues
    
    def start_services(self, log_widget=None):
        """启动所有服务"""
        self.log_message("🚀 开始启动 TradingAgents-CN...", log_widget)
        
        # 检查依赖
        issues = self.check_dependencies()
        if issues:
            error_msg = "发现以下问题:\n" + "\n".join(f"- {issue}" for issue in issues)
            self.log_message(f"❌ {error_msg}", log_widget)
            return False
        
        try:
            # 切换到项目目录
            os.chdir(self.project_root)
            self.log_message(f"📁 切换到项目目录: {self.project_root}", log_widget)

            # macOS专用启动命令
            conda_base = subprocess.run(
                ["conda", "info", "--base"],
                capture_output=True,
                text=True
            ).stdout.strip()

            conda_cmd = f"""
            source {conda_base}/etc/profile.d/conda.sh &&
            conda activate {self.conda_env} &&
            streamlit run start_web.py --server.port {self.web_port} --server.address 0.0.0.0 --server.headless true
            """
            shell_cmd = ["bash", "-c", conda_cmd]
            
            self.log_message("🔧 启动Streamlit Web服务...", log_widget)
            
            # 启动Streamlit
            process = subprocess.Popen(
                shell_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes.append(process)
            self.is_running = True
            
            # 等待服务启动
            self.log_message("⏳ 等待服务启动...", log_widget)
            time.sleep(5)
            
            # 检查服务状态
            if process.poll() is None:
                self.log_message("✅ Streamlit服务启动成功!", log_widget)
                self.log_message(f"🌐 Web界面地址: http://localhost:{self.web_port}", log_widget)
                
                # 自动打开浏览器
                self.log_message("🔗 正在打开浏览器...", log_widget)
                threading.Timer(2.0, lambda: webbrowser.open(f"http://localhost:{self.web_port}")).start()
                
                return True
            else:
                self.log_message("❌ Streamlit服务启动失败", log_widget)
                return False
                
        except Exception as e:
            self.log_message(f"❌ 启动过程中发生错误: {e}", log_widget)
            return False
    
    def stop_services(self, log_widget=None):
        """停止所有服务"""
        self.log_message("🛑 正在停止服务...", log_widget)
        
        # 停止Python进程
        for process in self.processes:
            if process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=5)
                    self.log_message("✅ 服务已停止", log_widget)
                except subprocess.TimeoutExpired:
                    process.kill()
                    self.log_message("⚠️ 强制停止服务", log_widget)
        
        # 清理进程列表
        self.processes.clear()
        self.is_running = False
        
        # macOS额外清理（针对可能残留的进程）
        try:
            subprocess.run(["pkill", "-f", "streamlit"],
                         capture_output=True, check=False)
            subprocess.run(["pkill", "-f", "start_web.py"],
                         capture_output=True, check=False)
        except:
            pass
    
    def run_tests(self, log_widget=None):
        """运行系统测试"""
        self.log_message("🧪 运行系统测试...", log_widget)
        
        try:
            os.chdir(self.project_root)

            # macOS专用测试命令
            conda_base = subprocess.run(
                ["conda", "info", "--base"],
                capture_output=True, text=True
            ).stdout.strip()

            cmd = f"source {conda_base}/etc/profile.d/conda.sh && conda activate {self.conda_env} && python test_system.py"
            result = subprocess.run(["bash", "-c", cmd],
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.log_message("✅ 系统测试通过", log_widget)
                return True
            else:
                self.log_message(f"❌ 系统测试失败: {result.stderr}", log_widget)
                return False
                
        except subprocess.TimeoutExpired:
            self.log_message("⏰ 测试超时", log_widget)
            return False
        except Exception as e:
            self.log_message(f"❌ 测试过程中发生错误: {e}", log_widget)
            return False

class TradingAgentsGUI:
    def __init__(self):
        self.launcher = TradingAgentsLauncher()
        self.root = tk.Tk()
        self.setup_gui()
    
    def setup_gui(self):
        """设置GUI界面"""
        self.root.title("TradingAgents-CN 启动器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置图标（如果有的话）
        try:
            # 可以添加图标文件
            pass
        except:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🚀 TradingAgents-CN 启动器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_frame = ttk.LabelFrame(main_frame, text="系统状态", padding="5")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                     font=("Arial", 10, "bold"))
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=2, sticky=tk.E)
        
        # 按钮
        self.start_btn = ttk.Button(button_frame, text="🚀 启动服务", 
                                   command=self.start_services, width=12)
        self.start_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_btn = ttk.Button(button_frame, text="🛑 停止服务", 
                                  command=self.stop_services, width=12, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 5))
        
        self.test_btn = ttk.Button(button_frame, text="🧪 运行测试", 
                                  command=self.run_tests, width=12)
        self.test_btn.grid(row=0, column=2, padx=(0, 5))
        
        self.browser_btn = ttk.Button(button_frame, text="🌐 打开浏览器", 
                                     command=self.open_browser, width=12, state="disabled")
        self.browser_btn.grid(row=0, column=3)
        
        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="5")
        log_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 初始化日志
        self.launcher.log_message("TradingAgents-CN 启动器已就绪", self.log_text)
        self.launcher.log_message(f"项目目录: {self.launcher.project_root}", self.log_text)
    
    def start_services(self):
        """启动服务"""
        self.status_var.set("启动中...")
        self.start_btn.config(state="disabled")
        self.test_btn.config(state="disabled")
        
        def start_thread():
            success = self.launcher.start_services(self.log_text)
            
            self.root.after(0, lambda: self.on_start_complete(success))
        
        threading.Thread(target=start_thread, daemon=True).start()
    
    def on_start_complete(self, success):
        """启动完成回调"""
        if success:
            self.status_var.set("运行中 ✅")
            self.stop_btn.config(state="normal")
            self.browser_btn.config(state="normal")
        else:
            self.status_var.set("启动失败 ❌")
            self.start_btn.config(state="normal")
            self.test_btn.config(state="normal")
    
    def stop_services(self):
        """停止服务"""
        self.launcher.stop_services(self.log_text)
        self.status_var.set("已停止")
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.browser_btn.config(state="disabled")
        self.test_btn.config(state="normal")
    
    def run_tests(self):
        """运行测试"""
        self.test_btn.config(state="disabled")
        
        def test_thread():
            self.launcher.run_tests(self.log_text)
            self.root.after(0, lambda: self.test_btn.config(state="normal"))
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def open_browser(self):
        """打开浏览器"""
        webbrowser.open(f"http://localhost:{self.launcher.web_port}")
        self.launcher.log_message("🔗 已打开浏览器", self.log_text)
    
    def on_closing(self):
        """关闭程序时的处理"""
        if self.launcher.is_running:
            if messagebox.askokcancel("退出", "服务正在运行，是否停止服务并退出？"):
                self.launcher.stop_services(self.log_text)
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """主函数"""
    print("🚀 TradingAgents-CN 启动器")
    print("=" * 50)
    
    # 检查是否有GUI参数
    use_gui = "--gui" in sys.argv or len(sys.argv) == 1
    
    if use_gui and GUI_AVAILABLE:
        print("启动图形界面...")
        app = TradingAgentsGUI()
        app.run()
    else:
        # 命令行模式
        print("使用命令行模式...")
        launcher = TradingAgentsLauncher()
        
        if "--test" in sys.argv:
            launcher.run_tests()
        elif "--stop" in sys.argv:
            launcher.stop_services()
        else:
            success = launcher.start_services()
            if success:
                print("\n✅ 服务启动成功!")
                print(f"🌐 Web界面: http://localhost:{launcher.web_port}")
                print("按 Ctrl+C 停止服务")
                
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n正在停止服务...")
                    launcher.stop_services()
                    print("服务已停止")

if __name__ == "__main__":
    main()
