#!/bin/bash
# TradingAgents-CN 自动备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/Users/<USER>/Desktop/TradingAgents-CN-main/backup"
PROJECT_DIR="/Users/<USER>/Desktop/TradingAgents-CN-main"

echo "开始备份 TradingAgents-CN 数据..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
echo "备份配置文件..."
tar -czf $BACKUP_DIR/config_$DATE.tar.gz -C $PROJECT_DIR config/ .env

# 备份数据目录
echo "备份数据目录..."
tar -czf $BACKUP_DIR/data_$DATE.tar.gz -C $PROJECT_DIR data/

# 备份MongoDB（如果运行）
if pgrep mongod > /dev/null; then
    echo "备份MongoDB数据..."
    mongodump --out $BACKUP_DIR/mongodb_$DATE
fi

# 备份Redis（如果运行）
if pgrep redis-server > /dev/null; then
    echo "备份Redis数据..."
    redis-cli --rdb $BACKUP_DIR/redis_$DATE.rdb
fi

# 清理旧备份（保留7天）
echo "清理旧备份文件..."
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "mongodb_*" -mtime +7 -exec rm -rf {} \;
find $BACKUP_DIR -name "redis_*.rdb" -mtime +7 -delete

echo "备份完成！备份文件保存在: $BACKUP_DIR"
