#!/usr/bin/env python3
"""
TradingAgents-CN 系统监控脚本
"""

import psutil
import time
import json
from datetime import datetime
from pathlib import Path

def get_system_metrics():
    """获取系统指标"""
    return {
        "timestamp": datetime.now().isoformat(),
        "cpu_percent": psutil.cpu_percent(interval=1),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_percent": psutil.disk_usage('/').percent,
        "network_io": dict(psutil.net_io_counters()._asdict()),
        "process_count": len(psutil.pids())
    }

def save_metrics(metrics, log_file):
    """保存指标到文件"""
    with open(log_file, "a") as f:
        f.write(json.dumps(metrics) + "\n")

def main():
    log_file = Path("logs/system_metrics.jsonl")
    log_file.parent.mkdir(exist_ok=True)
    
    print("🔍 开始系统监控...")
    print("按 Ctrl+C 停止监控")
    
    try:
        while True:
            metrics = get_system_metrics()
            save_metrics(metrics, log_file)
            
            print(f"CPU: {metrics['cpu_percent']:.1f}% | "
                  f"内存: {metrics['memory_percent']:.1f}% | "
                  f"磁盘: {metrics['disk_percent']:.1f}%")
            
            time.sleep(60)  # 每分钟记录一次
            
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == "__main__":
    main()
