#!/bin/bash
# TradingAgents-CN 启动脚本

cd /Users/<USER>/Desktop/TradingAgents-CN-main

echo "启动 TradingAgents-CN 服务..."

# 激活conda环境
source $(conda info --base)/etc/profile.d/conda.sh
conda activate tradingagents-cn

# 检查环境
echo "检查Python环境..."
python --version

# 启动Web服务
echo "启动Streamlit Web界面..."
streamlit run start_web.py --server.port 8501 --server.address 0.0.0.0 &

echo "TradingAgents-CN 已启动！"
echo "Web界面: http://localhost:8501"
echo "使用 'pkill -f streamlit' 停止服务"
