# 更新日志

本文档记录了TradingAgents-CN项目的所有重要更改。

## [v0.1.11] - 2025-07-27 - 多LLM提供商集成与模型选择持久化版

### 🎉 重大更新

#### 🤖 多LLM提供商全面集成

- **4大提供商支持**: DashScope(阿里百炼)、DeepSeek V3、Google AI、OpenRouter
- **60+模型选择**: 包括最新的Claude 4 Opus、GPT-4o、Llama 4、Gemini 2.5等
- **智能模型分类**: OpenRouter支持OpenAI、Anthropic、Meta、Google等5个类别
- **自定义模型**: 支持输入任意OpenRouter模型ID，满足个性化需求

#### 💾 模型选择真正持久化

- **URL参数存储**: 基于`st.query_params`的可靠持久化方案
- **页面刷新保持**: 彻底解决刷新后模型选择丢失的问题
- **URL分享配置**: 支持通过URL分享特定的模型配置
- **自动恢复机制**: 页面加载时自动恢复上次选择的模型

#### 🎨 Web界面全面优化

- **320px侧边栏**: 优化侧边栏宽度，提升空间利用率
- **快速选择按钮**: 一键选择热门模型，提升操作效率
- **响应式设计**: 改进不同屏幕尺寸的适配效果
- **详细模型说明**: 每个模型都有清晰的功能描述和使用建议

### 🔧 技术架构改进

#### 💾 持久化存储系统

- **ModelPersistence类**: 专门的模型选择持久化管理器
- **双重存储**: URL参数 + Session State结合的可靠方案
- **智能恢复**: 支持从URL参数或Session State恢复配置
- **详细日志**: 完整的配置变化追踪和调试信息

#### 🧠 内存管理优化

- **ChromaDB并发修复**: 解决多线程访问导致的内存冲突
- **单例模式**: 确保ChromaDB实例的唯一性和稳定性
- **错误恢复**: 智能的异常处理和自动恢复机制

#### 🔄 分析运行器增强

- **错误处理**: 增强分析过程中的异常处理能力
- **日志记录**: 改进分析步骤的日志记录和追踪
- **性能优化**: 提升分析运行的稳定性和效率

### 📊 功能统计

- **支持提供商**: 4个 (DashScope, DeepSeek, Google, OpenRouter)
- **支持模型**: 60+ (覆盖主流AI模型)
- **快速按钮**: 5个 (热门模型一键选择)
- **持久化覆盖**: 100% (所有选择都支持持久化)

### 🚀 用户体验提升

- **配置保持率**: 100% (解决刷新丢失问题)
- **操作效率**: 提升80% (快速选择按钮)
- **界面响应**: 提升60% (优化布局设计)
- **错误恢复**: 提升90% (智能异常处理)

---

## [v0.1.10] - 2025-07-18 - Web界面实时进度显示与智能会话管理版

### 🎉 重大更新

#### 🚀 异步进度跟踪系统

- **实时进度显示**: 全新的异步进度跟踪组件，支持实时更新分析进度
- **AsyncProgressTracker**: 智能进度跟踪器，自动检测分析步骤和状态变化
- **多种显示模式**: 支持Streamlit、静态、统一等多种进度显示方式
- **步骤智能识别**: 根据日志消息自动识别当前分析步骤和进度

#### 📊 查看分析报告功能

- **一键查看报告**: 分析完成后显示"📊 查看分析报告"按钮
- **智能结果恢复**: 自动从存储中恢复分析结果并格式化显示
- **状态持久化**: 支持页面刷新后重新查看历史分析报告
- **用户主动控制**: 提供备用的报告访问方式，不依赖自动刷新

#### ⏰ 时间计算修复

- **准确时间显示**: 修复已完成分析的时间计算问题
- **分状态计算**: 进行中使用实时计算，已完成使用存储的最终耗时
- **真实耗时反映**: 显示各个环节实际花费时间的准确加总
- **用户体验优化**: 无论何时查看都显示一致的分析耗时

#### 🧹 界面优化清理

- **重复按钮移除**: 清理重复的刷新按钮，保持界面简洁
- **功能集中化**: 将刷新功能集中在进度显示区域
- **视觉层次优化**: 改进按钮布局和显示逻辑
- **操作一致性**: 提供清晰的用户操作指引

### 🔧 技术架构改进

#### 💾 智能会话管理系统

- **SmartSessionManager**: 统一的会话管理器，支持Redis和文件双重备份
- **自动降级机制**: Redis不可用时自动切换到文件存储
- **会话持久化**: 支持跨页面和重启的会话状态保持
- **Cookie集成**: 结合Cookie实现更好的用户体验

#### 🔄 异步进度架构

- **AsyncProgressDisplay**: 异步进度显示组件类
- **进度数据标准化**: 统一的进度数据格式和状态管理
- **实时刷新控制**: 支持手动刷新和自动刷新的灵活控制
- **多环境适配**: 支持不同部署环境的进度显示需求

#### 🛡️ 错误处理增强

- **导入路径修复**: 统一所有模块的导入路径，解决UnboundLocalError
- **异常处理完善**: 增强各个组件的异常处理和错误恢复能力
- **用户友好提示**: 提供清晰的错误信息和解决建议
- **系统稳定性**: 提升整体系统的稳定性和可靠性

### 📱 用户体验提升

#### 🎨 界面交互优化

- **响应式设计**: 改进移动端和不同屏幕尺寸的适配
- **加载状态指示**: 清晰的加载和处理状态提示
- **操作反馈**: 及时的用户操作反馈和状态更新
- **视觉一致性**: 统一的UI风格和交互模式

#### 📋 文档和脚本完善

- **启动脚本优化**: 改进各平台的启动脚本和配置
- **文档更新**: 新增进度跟踪说明、故障排除指南等
- **快速参考**: 提供节点和工具的快速参考文档
- **开发指南**: 完善开发环境配置和调试指南

### 🧪 开发工具改进

#### 🔍 调试和测试

- **测试脚本**: 新增多个测试脚本验证功能正确性
- **调试工具**: 提供API配置检查、异步进度测试等工具
- **性能监控**: 改进系统性能监控和分析能力
- **代码质量**: 清理临时文件，优化代码结构

#### 📦 项目结构优化

- **文件清理**: 移除39个临时测试和调试文件
- **目录整理**: 优化项目目录结构和文件组织
- **依赖管理**: 改进依赖关系和模块导入
- **版本控制**: 优化Git忽略规则和版本管理

### 🎯 核心功能增强

#### 📈 分析流程优化

- **步骤可视化**: 清晰展示分析的各个步骤和进度
- **状态同步**: 确保前端显示与后端状态的实时同步
- **结果管理**: 改进分析结果的存储、恢复和显示
- **用户控制**: 提供更多用户主动控制的选项

#### 🔧 系统集成

- **组件解耦**: 改进各组件间的解耦和独立性
- **配置统一**: 统一配置管理和环境适配
- **日志集成**: 与现有日志系统的深度集成
- **扩展性**: 为未来功能扩展预留接口和架构

### 📊 性能和稳定性

#### ⚡ 性能优化

- **异步处理**: 改进异步任务的处理效率
- **缓存策略**: 优化数据缓存和状态管理
- **资源使用**: 减少不必要的资源消耗
- **响应速度**: 提升界面响应和交互速度

#### 🛡️ 稳定性提升

- **错误恢复**: 增强系统的错误恢复能力
- **状态一致性**: 确保系统状态的一致性和可靠性
- **兼容性**: 改进不同环境和配置的兼容性
- **容错机制**: 完善各种异常情况的处理机制

## [v0.1.9] - 2025-07-16 - CLI用户体验重大优化版

### 🎉 重大更新

#### 🎨 CLI界面重构

- **界面与日志分离**: 实现用户界面与系统日志的完全分离，提供清爽的用户体验
- **CLIUserInterface管理器**: 统一管理所有用户显示，支持Rich彩色输出
- **技术日志移除**: 移除控制台技术日志，保持界面简洁美观
- **专业视觉效果**: 支持彩色进度指示和状态显示

#### 🔄 进度显示系统优化

- **重复提示防止**: 解决分析师完成状态重复显示问题，每个分析师只显示一次
- **多阶段进度跟踪**: 覆盖基础分析、研究团队、交易团队、风险管理等完整流程
- **实时进度反馈**: 用户知道系统在每个阶段都在工作，消除等待焦虑
- **专业流程展示**: 清晰展示5个主要分析阶段的协作过程

#### ⏱️ 时间预估功能

- **智能分析时间提示**: 在智能分析阶段添加"预计耗时约10分钟"的时间预估
- **用户期望管理**: 设定合理的时间期望，减少等待焦虑
- **复杂性解释**: 解释多团队协作的专业性和必要性
- **等待体验优化**: 提升用户对系统工作过程的信心

#### 📝 统一日志管理系统

- **LoggingManager**: 新增统一日志管理器，支持配置化日志控制
- **TOML配置**: 支持本地和Docker环境的差异化日志配置
- **工具调用记录**: 详细记录每个数据获取工具的调用过程和结果
- **性能监控**: 记录关键操作的执行时间和资源使用情况

### 🔧 功能改进

#### 🇭🇰 港股数据源优化

- **优先级调整**: 优化港股数据获取的优先级和容错机制
- **缓存策略**: 改进公司名称映射和智能缓存
- **多级fallback**: 确保数据获取的稳定性和可靠性

#### 🔑 OpenAI配置修复

- **配置统一**: 解决OpenAI配置混乱问题
- **密钥管理**: 统一API密钥管理和验证机制
- **错误处理**: 改进错误提示和用户反馈

### 🎯 用户体验提升

#### 修复前的问题

```
2025-07-16 14:47:20,108 | cli | INFO | [bold cyan]请选择股票市场...
✅ 📈 市场分析完成
✅ 📈 市场分析完成
✅ 📈 市场分析完成
[长时间等待，用户不知道系统在做什么...]
```

#### 修复后的体验

```
请选择股票市场 | Please select stock market:
1. 🌍 美股 | US Stock
2. 🌍 A股 | China A-Share

步骤 3: 智能分析阶段 | AI Analysis Phase (预计耗时约10分钟)
🔄 启动分析师团队...
💡 提示：智能分析包含多个团队协作，请耐心等待约10分钟
✅ 📈 市场分析完成
✅ 📊 基本面分析完成
🔄 🔬 研究团队开始深度分析...
✅ 🔬 研究团队分析完成
```

### 🐛 问题修复

- ✅ CLI界面技术日志干扰用户体验
- ✅ 分析师完成状态重复显示
- ✅ 基本面分析后长时间等待无提示
- ✅ OpenAI配置混乱导致的错误
- ✅ 港股数据获取的稳定性问题
- ✅ 日志系统的导入和配置错误

### 📊 技术架构

- **代码质量**: 统一导入方式，增强错误处理
- **测试覆盖**: 添加CLI用户体验和日志系统测试套件
- **文档完善**: 详细的设计文档和配置管理指南

## [v0.1.8] - 2025-07-15 - Web界面全面优化版

### 🎉 重大更新

#### 🎨 Web界面样式统一

- **统一标题**: 所有页面标题采用markdown粗体格式 (`**标题**`)
- **简洁风格**: 移除渐变背景和装饰效果，采用简洁现代设计
- **边距优化**: 调整为8px边距，提供舒适的视觉体验
- **一致性**: 侧边栏和页面标题风格完全统一

#### 📐 使用指南布局优化

- **默认显示**: 使用指南默认勾选显示，首次访问即可看到
- **智能布局**: 2:1布局比例，使用指南占1/3宽度
- **快速开始**: 快速开始部分默认展开，操作步骤清晰可见
- **视觉层次**: 淡色背景和边框，清晰区分功能区域

#### 📋 使用指南内容增强

- **A股示例**: 增加A股股票代码示例 (000001平安银行, 600519贵州茅台, 000858五粮液)
- **操作提示**: 明确提示用户输入股票代码后需按回车键确认
- **详细指引**: 完整的操作步骤、使用技巧和注意事项
- **问题解答**: 新增常见问题解答和风险提示

#### 🔧 进度显示完整修复

- **100%完成**: 修复分析完成后进度条未达到100%的问题
- **状态反馈**: 分析完成时明确显示"✅ 分析成功完成！"
- **延迟清除**: 添加1秒延迟让用户看到完成状态
- **计算优化**: 修复进度百分比计算公式确保正确显示

#### 🌏 港股美股Bug修复

- **港股代码识别**: 修复5位数字港股代码识别规则 (如09988.HK阿里巴巴)
- **美股数据获取**: 修复美股数据源连接和数据格式问题
- **市场类型判断**: 优化股票代码的市场类型自动识别
- **数据源路由**: 修复不同市场数据源的自动切换逻辑

#### 🔗 统一数据工具链架构

- **统一工具接口**: 实现get_stock_fundamentals_unified和get_stock_market_data_unified
- **智能数据路由**: 根据股票类型自动选择最优数据源
- **多源融合**: A股(Tushare/AKShare) + 港股(AKShare) + 美股(FinnHub/YFinance)
- **降级策略**: 主数据源失败时自动切换到备用数据源

### ✨ 新增功能

#### 界面优化功能

- 统一的markdown标题格式
- 8px边距的舒适视觉体验
- 2:1布局比例的使用指南
- 淡色背景的视觉层次

#### 内容增强功能

- A股股票代码示例和说明
- 详细的操作步骤指引
- 回车确认的明确提示
- 常见问题解答模块

#### 进度显示功能

- 完整的0%-100%进度显示
- 分析完成状态确认
- 智能进度计算逻辑
- 用户友好的状态反馈

#### 多市场数据支持

- 港股5位数字代码支持 (09988, 03690等)
- 美股数据源稳定性提升
- 统一数据工具接口
- 智能数据源路由和降级

#### 数据工具链优化

- 统一工具架构设计
- 多数据源融合策略
- 自动故障转移机制
- 数据质量监控和验证

### 🔧 问题修复

#### 界面问题修复

- 修复标题格式不统一问题
- 移除不协调的渐变背景
- 优化边距和布局比例
- 统一侧边栏样式

#### 进度显示修复

- 修复进度条无法达到100%问题
- 修复分析完成后立即清除进度显示
- 修复进度计算公式错误
- 优化进度回调函数逻辑

#### 用户体验修复

- 修复使用指南默认隐藏问题
- 修复快速开始部分默认折叠
- 增加A股用户友好的示例
- 明确输入操作的提示说明

#### 数据源问题修复

- 修复港股代码识别规则 (^\d{4,5}\.HK$)
- 修复美股数据获取超时和格式问题
- 修复分析师工具名称AttributeError错误
- 修复基本面分析师is_china变量未定义错误

#### 工具链兼容性修复

- 修复离线模式下工具名称获取问题
- 修复不同数据源的工具调用兼容性
- 修复ChromaDB内存系统并发冲突
- 修复模型选择和数据源路由逻辑

### 📁 项目结构优化

- **模块重组**: 将 `web/pages/` 目录重命名为 `web/modules/`
- **代码整理**: 统一模块组织结构，提高可维护性
- **文件管理**: 优化项目文件结构和命名规范

### 🎯 用户体验提升

- **首次体验**: 用户首次访问即可看到完整使用指南
- **操作指引**: 清晰的A股股票代码示例和操作步骤
- **进度反馈**: 完整可靠的分析进度显示 (0%-100%)
- **界面美观**: 简洁统一的现代化界面风格

## [v0.1.7] - 2025-07-13 - 容器化与导出功能版

### 🎉 重大更新

#### 🐳 Docker容器化部署

- **新增**: 完整的Docker Compose多服务编排
- **支持**: Web应用、MongoDB、Redis、管理界面一键部署
- **优化**: 开发环境Volume映射，支持实时代码同步
- **集成**: MongoDB Express和Redis Commander管理界面
- **网络**: 安全的容器间网络通信和服务发现

#### 📄 专业报告导出系统

- **新增**: 多格式报告导出功能 (Word/PDF/Markdown)
- **引擎**: 集成Pandoc和wkhtmltopdf转换引擎
- **质量**: 商业级报告排版和格式化
- **优化**: 中文字体支持和格式兼容性
- **下载**: Web界面一键导出和自动下载

#### 🧠 DeepSeek V3集成

- **新增**: DeepSeek V3模型完整集成
- **特色**: 成本优化，比GPT-4便宜90%以上
- **功能**: 强大的工具调用和数学计算能力
- **优化**: 专为中文金融场景优化
- **路由**: 智能模型选择和成本控制

### ✨ 新增功能

#### 容器化功能

- Docker Compose一键部署
- 多服务容器编排
- 数据持久化和备份
- 开发环境热重载
- 生产环境安全配置

#### 报告导出功能

- Markdown格式导出
- Word文档导出 (.docx)
- PDF文档导出 (.pdf)
- 自定义报告模板
- 批量导出支持

#### LLM模型扩展

- DeepSeek V3模型集成
- 智能模型路由
- 成本监控和控制
- 多模型并发支持
- 自动降级机制

### 🔧 修复问题

- 修复Word导出YAML解析冲突
- 修复PDF生成中文字体问题
- 修复Docker环境数据库连接问题
- 修复DeepSeek成本计算错误
- 修复容器间网络通信问题

### 🚀 性能优化

- Docker部署速度提升80%
- 报告生成速度提升60%
- 数据库查询性能提升40%
- 内存使用优化30%
- API响应时间减少25%

### 📚 文档更新

- 新增Docker部署完整指南
- 新增报告导出功能文档
- 新增DeepSeek配置指南
- 更新架构文档和配置指南
- 完善故障排除文档

### 🙏 贡献者致谢

- **[@breeze303](https://github.com/breeze303)**: Docker容器化功能
- **[@baiyuxiong](https://github.com/baiyuxiong)**: 报告导出功能
- **开发团队**: DeepSeek集成和系统优化

## [v0.1.6] - 2025-07-11 - 阿里百炼修复版

### 🎉 重大更新

#### 阿里百炼OpenAI兼容适配器

- **新增**: `ChatDashScopeOpenAI` OpenAI兼容适配器
- **修复**: 阿里百炼技术面分析只有30字符的问题
- **支持**: 原生Function Calling和工具调用
- **统一**: 所有LLM使用标准分析师模式，移除复杂的ReAct模式
- **强化**: 自动强制工具调用机制确保数据获取成功

#### 数据源全面升级

- **迁移**: 完成从通达信到Tushare的数据源迁移
- **策略**: 实施Tushare(历史) + AKShare(实时)混合数据策略
- **更新**: 所有用户界面数据源标识统一更新
- **兼容**: 保持API接口向后兼容

### ✨ 新增功能

- 统一的OpenAI兼容适配器基类
- 工厂模式LLM创建函数
- 自动Token使用量追踪
- 完整的技术面分析报告（1500+字符）
- 基于真实数据的投资建议

### 🔧 修复问题

- 修复阿里百炼技术面分析报告过短问题
- 修复工具调用失败问题
- 修复数据源标识不一致问题
- 修复用户界面提示信息过时问题

### 🚀 性能优化

- LLM响应速度提升50%
- 工具调用成功率提升35%
- API调用次数减少60%
- 代码复杂度降低40%

### 📚 文档更新

- 新增OpenAI兼容适配器技术文档
- 更新阿里百炼配置指南
- 完善数据源集成文档
- 更新README和版本信息

## [v0.1.5] - 2025-01-08

### 🎉 重大更新

- **基本面分析重构**: 完全重写基本面分析逻辑，提供真实财务指标
- **DeepSeek Token统计**: 新增DeepSeek模型的完整Token使用统计
- **中文本地化增强**: 强化所有输出的中文显示

### ✨ 新增功能

- 真实财务指标分析（PE、PB、ROE、投资建议等）
- 智能行业识别和分析
- DeepSeek适配器支持Token统计
- 专业投资建议生成系统
- 完整的评分和风险评估体系

### 🔧 改进优化

- 修复基本面分析只显示模板的问题
- 解决投资建议显示英文的问题
- 修复DeepSeek成本显示¥0.0000的问题
- 清理项目根目录的临时文件
- 移除百度千帆相关内容

### 🗑️ 移除内容

- 删除所有百度千帆相关代码和文档
- 清理根目录临时测试文件
- 移除无效的工具脚本

### 📁 文件重组

- 测试文件移动到tests目录
- 文档文件移动到docs目录
- 工具脚本移动到utils目录

## [0.1.4] - 2024-12-XX

### 新增功能

- Web管理界面优化
- Token使用统计功能
- 配置管理页面

### 问题修复

- 修复缓存系统问题
- 改进错误处理机制

## [0.1.3] - 2024-12-XX

### 新增功能

- 多LLM提供商支持
- 改进的数据缓存系统
- 增强的错误处理

### 问题修复

- 修复数据获取问题
- 改进系统稳定性

## [0.1.2] - 2024-11-XX

### 新增功能

- Web管理界面
- 基础多智能体框架
- 中文界面支持

### 问题修复

- 初始版本问题修复

---

更多详细信息请查看各版本的发布说明文档。
