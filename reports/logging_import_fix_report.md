
# 日志导入位置修复报告

## 修复统计
- 成功修复文件: 103
- 错误数量: 0

## 修复的文件
- C:\code\TradingAgentsCN\fix_stock_code_issue.py
- C:\code\TradingAgentsCN\main.py
- C:\code\TradingAgentsCN\quick_syntax_check.py
- C:\code\TradingAgentsCN\stock_code_validator.py
- C:\code\TradingAgentsCN\syntax_checker.py
- C:\code\TradingAgentsCN\cli\main.py
- C:\code\TradingAgentsCN\cli\utils.py
- C:\code\TradingAgentsCN\data\scripts\sync_stock_info_to_mongodb.py
- C:\code\TradingAgentsCN\examples\batch_analysis.py
- C:\code\TradingAgentsCN\examples\cli_demo.py
- C:\code\TradingAgentsCN\examples\config_management_demo.py
- C:\code\TradingAgentsCN\examples\custom_analysis_demo.py
- C:\code\TradingAgentsCN\examples\data_dir_config_demo.py
- C:\code\TradingAgentsCN\examples\demo_deepseek_analysis.py
- C:\code\TradingAgentsCN\examples\my_stock_analysis.py
- C:\code\TradingAgentsCN\examples\simple_analysis_demo.py
- C:\code\TradingAgentsCN\examples\stock_list_example.py
- C:\code\TradingAgentsCN\examples\stock_query_examples.py
- C:\code\TradingAgentsCN\examples\token_tracking_demo.py
- C:\code\TradingAgentsCN\examples\tushare_demo.py
- C:\code\TradingAgentsCN\examples\dashscope_examples\demo_dashscope.py
- C:\code\TradingAgentsCN\examples\dashscope_examples\demo_dashscope_chinese.py
- C:\code\TradingAgentsCN\examples\dashscope_examples\demo_dashscope_no_memory.py
- C:\code\TradingAgentsCN\examples\dashscope_examples\demo_dashscope_simple.py
- C:\code\TradingAgentsCN\examples\openai\demo_openai.py
- C:\code\TradingAgentsCN\scripts\analyze_data_calls.py
- C:\code\TradingAgentsCN\scripts\build_docker_with_pdf.py
- C:\code\TradingAgentsCN\scripts\install_pandoc.py
- C:\code\TradingAgentsCN\scripts\install_pdf_tools.py
- C:\code\TradingAgentsCN\scripts\log_analyzer.py
- C:\code\TradingAgentsCN\scripts\migrate_to_unified_logging.py
- C:\code\TradingAgentsCN\scripts\setup-docker.py
- C:\code\TradingAgentsCN\scripts\deployment\create_github_release.py
- C:\code\TradingAgentsCN\scripts\deployment\release_v0.1.2.py
- C:\code\TradingAgentsCN\scripts\deployment\release_v0.1.3.py
- C:\code\TradingAgentsCN\scripts\development\adaptive_cache_manager.py
- C:\code\TradingAgentsCN\scripts\development\download_finnhub_sample_data.py
- C:\code\TradingAgentsCN\scripts\development\fix_streamlit_watcher.py
- C:\code\TradingAgentsCN\scripts\development\organize_scripts.py
- C:\code\TradingAgentsCN\scripts\development\prepare_upstream_contribution.py
- C:\code\TradingAgentsCN\scripts\git\branch_manager.py
- C:\code\TradingAgentsCN\scripts\git\check_branch_overlap.py
- C:\code\TradingAgentsCN\scripts\maintenance\branch_manager.py
- C:\code\TradingAgentsCN\scripts\maintenance\cleanup_cache.py
- C:\code\TradingAgentsCN\scripts\maintenance\finalize_script_organization.py
- C:\code\TradingAgentsCN\scripts\maintenance\organize_root_scripts.py
- C:\code\TradingAgentsCN\scripts\maintenance\sync_upstream.py
- C:\code\TradingAgentsCN\scripts\maintenance\version_manager.py
- C:\code\TradingAgentsCN\scripts\setup\configure_pip_source.py
- C:\code\TradingAgentsCN\scripts\setup\initialize_system.py
- C:\code\TradingAgentsCN\scripts\setup\init_database.py
- C:\code\TradingAgentsCN\scripts\setup\manual_pip_config.py
- C:\code\TradingAgentsCN\scripts\setup\migrate_env_to_config.py
- C:\code\TradingAgentsCN\scripts\setup\setup_databases.py
- C:\code\TradingAgentsCN\scripts\validation\check_dependencies.py
- C:\code\TradingAgentsCN\scripts\validation\check_system_status.py
- C:\code\TradingAgentsCN\scripts\validation\smart_config.py
- C:\code\TradingAgentsCN\scripts\validation\verify_gitignore.py
- C:\code\TradingAgentsCN\tradingagents\agents\utils\agent_utils.py
- C:\code\TradingAgentsCN\tradingagents\api\stock_api.py
- C:\code\TradingAgentsCN\tradingagents\config\config_manager.py
- C:\code\TradingAgentsCN\tradingagents\config\mongodb_storage.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\akshare_utils.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\cache_manager.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\data_source_manager.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\db_cache_manager.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\finnhub_utils.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\googlenews_utils.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\hk_stock_utils.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\interface.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\optimized_china_data.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\optimized_us_data.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\realtime_news_utils.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\stock_api.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\stock_data_service.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\tdx_utils.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\tushare_utils.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\utils.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\yfin_utils.py
- C:\code\TradingAgentsCN\tradingagents\dataflows\__init__.py
- C:\code\TradingAgentsCN\tradingagents\graph\trading_graph.py
- C:\code\TradingAgentsCN\tradingagents\llm_adapters\dashscope_adapter.py
- C:\code\TradingAgentsCN\tradingagents\llm_adapters\dashscope_openai_adapter.py
- C:\code\TradingAgentsCN\tradingagents\llm_adapters\deepseek_adapter.py
- C:\code\TradingAgentsCN\tradingagents\llm_adapters\openai_compatible_base.py
- C:\code\TradingAgentsCN\tradingagents\utils\logging_manager.py
- C:\code\TradingAgentsCN\tradingagents\utils\tool_logging.py
- C:\code\TradingAgentsCN\upstream_contribution\batch1_caching\tradingagents\dataflows\cache_manager.py
- C:\code\TradingAgentsCN\upstream_contribution\batch1_caching\tradingagents\dataflows\optimized_us_data.py
- C:\code\TradingAgentsCN\upstream_contribution\batch2_error_handling\tradingagents\agents\analysts\fundamentals_analyst.py
- C:\code\TradingAgentsCN\upstream_contribution\batch2_error_handling\tradingagents\agents\analysts\market_analyst.py
- C:\code\TradingAgentsCN\upstream_contribution\batch2_error_handling\tradingagents\dataflows\db_cache_manager.py
- C:\code\TradingAgentsCN\upstream_contribution\batch3_data_sources\tradingagents\dataflows\optimized_us_data.py
- C:\code\TradingAgentsCN\utils\check_version_consistency.py
- C:\code\TradingAgentsCN\utils\cleanup_unnecessary_dirs.py
- C:\code\TradingAgentsCN\utils\update_data_source_references.py
- C:\code\TradingAgentsCN\web\app.py
- C:\code\TradingAgentsCN\web\run_web.py
- C:\code\TradingAgentsCN\web\components\analysis_form.py
- C:\code\TradingAgentsCN\web\components\results_display.py
- C:\code\TradingAgentsCN\web\utils\analysis_runner.py
- C:\code\TradingAgentsCN\web\utils\docker_pdf_adapter.py
- C:\code\TradingAgentsCN\web\utils\report_exporter.py
