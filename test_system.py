#!/usr/bin/env python3
"""
TradingAgents-CN 系统测试脚本
测试系统的核心功能是否正常工作
"""

import os
import sys
import traceback
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_data_sources():
    """测试数据源"""
    print("=" * 50)
    print("测试数据源")
    print("=" * 50)
    
    # 测试 AKShare
    try:
        import akshare as ak
        print("✓ AKShare 导入成功")
        
        # 获取上证指数数据
        data = ak.stock_zh_index_daily(symbol='sh000001')
        print(f"✓ 成功获取上证指数数据，共 {len(data)} 条记录")
        print(f"  最新数据日期: {data.iloc[-1]['date']}")
        print(f"  最新收盘价: {data.iloc[-1]['close']}")
        
    except Exception as e:
        print(f"✗ AKShare 测试失败: {e}")
    
    # 测试 Tushare
    try:
        import tushare as ts
        print("✓ Tushare 导入成功")
        
        # 注意：Tushare需要token才能使用，这里只测试导入
        token = os.getenv('TUSHARE_TOKEN')
        if token and token != 'your_tushare_token_here':
            ts.set_token(token)
            pro = ts.pro_api()
            print("✓ Tushare API 初始化成功")
        else:
            print("⚠ Tushare token 未配置，跳过API测试")
            
    except Exception as e:
        print(f"✗ Tushare 测试失败: {e}")
    
    # 测试 yfinance
    try:
        import yfinance as yf
        print("✓ yfinance 导入成功")
        
        # 简单测试，避免频率限制
        ticker = yf.Ticker('AAPL')
        info = ticker.info
        if info:
            print("✓ yfinance 基本功能正常")
        
    except Exception as e:
        print(f"⚠ yfinance 测试: {e}")

def test_ai_models():
    """测试AI模型"""
    print("\n" + "=" * 50)
    print("测试AI模型")
    print("=" * 50)
    
    # 测试 OpenAI
    try:
        from openai import OpenAI
        print("✓ OpenAI 库导入成功")
        
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key and api_key != 'your_openai_api_key_here':
            client = OpenAI(api_key=api_key)
            print("✓ OpenAI 客户端初始化成功")
        else:
            print("⚠ OpenAI API key 未配置，跳过API测试")
            
    except Exception as e:
        print(f"✗ OpenAI 测试失败: {e}")
    
    # 测试 LangChain
    try:
        from langchain_community.llms import OpenAI as LangChainOpenAI
        from langchain_openai import ChatOpenAI
        print("✓ LangChain 导入成功")
        
    except Exception as e:
        print(f"✗ LangChain 测试失败: {e}")
    
    # 测试 Anthropic
    try:
        import anthropic
        print("✓ Anthropic 库导入成功")
        
        api_key = os.getenv('ANTHROPIC_API_KEY')
        if api_key and api_key != 'your_anthropic_api_key_here':
            client = anthropic.Anthropic(api_key=api_key)
            print("✓ Anthropic 客户端初始化成功")
        else:
            print("⚠ Anthropic API key 未配置，跳过API测试")
            
    except Exception as e:
        print(f"✗ Anthropic 测试失败: {e}")

def test_database():
    """测试数据库连接"""
    print("\n" + "=" * 50)
    print("测试数据库")
    print("=" * 50)
    
    # 测试 ChromaDB
    try:
        import chromadb
        print("✓ ChromaDB 导入成功")
        
        # 创建临时客户端测试
        client = chromadb.Client()
        print("✓ ChromaDB 客户端创建成功")
        
    except Exception as e:
        print(f"✗ ChromaDB 测试失败: {e}")
    
    # 测试 Redis
    try:
        import redis
        print("✓ Redis 库导入成功")
        
        # 尝试连接本地Redis（如果运行的话）
        try:
            r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=1)
            r.ping()
            print("✓ Redis 连接成功")
        except:
            print("⚠ Redis 服务未运行，跳过连接测试")
            
    except Exception as e:
        print(f"✗ Redis 测试失败: {e}")
    
    # 测试 MongoDB
    try:
        import pymongo
        print("✓ PyMongo 导入成功")
        
        # 尝试连接本地MongoDB（如果运行的话）
        try:
            client = pymongo.MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=1000)
            client.server_info()
            print("✓ MongoDB 连接成功")
        except:
            print("⚠ MongoDB 服务未运行，跳过连接测试")
            
    except Exception as e:
        print(f"✗ MongoDB 测试失败: {e}")

def test_web_framework():
    """测试Web框架"""
    print("\n" + "=" * 50)
    print("测试Web框架")
    print("=" * 50)
    
    try:
        import streamlit as st
        print("✓ Streamlit 导入成功")
        
    except Exception as e:
        print(f"✗ Streamlit 测试失败: {e}")
    
    try:
        import chainlit as cl
        print("✓ Chainlit 导入成功")
        
    except Exception as e:
        print(f"✗ Chainlit 测试失败: {e}")

def test_trading_libraries():
    """测试交易相关库"""
    print("\n" + "=" * 50)
    print("测试交易相关库")
    print("=" * 50)
    
    try:
        import backtrader as bt
        print("✓ Backtrader 导入成功")
        
    except Exception as e:
        print(f"✗ Backtrader 测试失败: {e}")
    
    try:
        import stockstats
        print("✓ StockStats 导入成功")
        
    except Exception as e:
        print(f"✗ StockStats 测试失败: {e}")

def main():
    """主测试函数"""
    print("TradingAgents-CN 系统测试")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("Python 版本:", sys.version)
    
    try:
        test_data_sources()
        test_ai_models()
        test_database()
        test_web_framework()
        test_trading_libraries()
        
        print("\n" + "=" * 50)
        print("测试完成！")
        print("=" * 50)
        print("✓ 表示功能正常")
        print("⚠ 表示需要配置或服务未运行")
        print("✗ 表示存在问题")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
