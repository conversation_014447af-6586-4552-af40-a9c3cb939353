#!/usr/bin/env python3
"""
测试macOS应用程序是否正常工作
"""

import subprocess
import time
import requests
from pathlib import Path

def test_app():
    """测试应用程序"""
    print("🧪 测试TradingAgents-CN macOS应用程序")
    print("=" * 50)
    
    # 1. 检查应用程序是否存在
    app_path = Path("/Users/<USER>/Desktop/TradingAgents-CN.app")
    if not app_path.exists():
        print("❌ 应用程序不存在")
        return False
    
    print("✅ 应用程序文件存在")
    
    # 2. 停止现有服务
    print("🛑 停止现有服务...")
    subprocess.run(["pkill", "-f", "streamlit"], capture_output=True)
    time.sleep(2)
    
    # 3. 启动应用程序
    print("🚀 启动应用程序...")
    process = subprocess.Popen(
        ["open", str(app_path)],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    # 4. 等待服务启动
    print("⏳ 等待服务启动...")
    max_wait = 30
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:8501", timeout=2)
            if response.status_code == 200:
                print("✅ 服务启动成功!")
                print(f"🌐 Web界面可访问: http://localhost:8501")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 5 == 0 and i > 0:
            print(f"⏳ 等待中... ({i}/{max_wait})")
    
    print("❌ 服务启动超时")
    return False

def main():
    """主函数"""
    success = test_app()
    
    if success:
        print("\n🎉 macOS应用程序测试成功!")
        print("💡 您现在可以:")
        print("  1. 双击桌面上的TradingAgents-CN.app启动")
        print("  2. 访问 http://localhost:8501 使用系统")
        print("  3. 将应用程序拖拽到Dock栏")
    else:
        print("\n😞 macOS应用程序测试失败")
        print("💡 请检查:")
        print("  1. conda环境是否正确")
        print("  2. 依赖是否完整安装")
        print("  3. 查看日志: logs/app_launcher.log")

if __name__ == "__main__":
    main()
