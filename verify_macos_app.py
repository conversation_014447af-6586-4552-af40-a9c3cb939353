#!/usr/bin/env python3
"""
验证macOS应用程序是否正常工作
"""

import requests
import subprocess
from pathlib import Path

def verify_app():
    """验证应用程序状态"""
    print("🔍 验证TradingAgents-CN macOS应用程序状态")
    print("=" * 50)
    
    # 1. 检查应用程序文件
    app_path = Path("/Users/<USER>/Desktop/TradingAgents-CN.app")
    if app_path.exists():
        print("✅ 应用程序文件存在")
    else:
        print("❌ 应用程序文件不存在")
        return False
    
    # 2. 检查服务状态
    try:
        response = requests.get("http://localhost:8501", timeout=5)
        if response.status_code == 200:
            print("✅ Web服务正在运行")
            print("🌐 可访问地址: http://localhost:8501")
        else:
            print(f"⚠️  Web服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Web服务无法访问: {e}")
        return False
    
    # 3. 检查进程
    try:
        result = subprocess.run(
            ["ps", "aux"], 
            capture_output=True, 
            text=True
        )
        
        streamlit_processes = [
            line for line in result.stdout.split('\n') 
            if 'streamlit' in line and 'grep' not in line
        ]
        
        if streamlit_processes:
            print(f"✅ 发现 {len(streamlit_processes)} 个Streamlit进程")
            for i, process in enumerate(streamlit_processes, 1):
                # 只显示关键信息
                parts = process.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    print(f"   进程 {i}: PID {pid}")
        else:
            print("⚠️  未发现Streamlit进程")
    
    except Exception as e:
        print(f"⚠️  进程检查失败: {e}")
    
    # 4. 检查日志
    log_file = Path("/Users/<USER>/Desktop/TradingAgents-CN-main/logs/app_launcher.log")
    if log_file.exists():
        print("✅ 日志文件存在")
        
        # 读取最后几行日志
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print("📋 最近的日志:")
                    for line in lines[-3:]:
                        print(f"   {line.strip()}")
        except Exception as e:
            print(f"⚠️  读取日志失败: {e}")
    else:
        print("⚠️  日志文件不存在")
    
    return True

def main():
    """主函数"""
    success = verify_app()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 macOS应用程序验证成功!")
        print("\n💡 使用方法:")
        print("1. 双击桌面上的 TradingAgents-CN.app")
        print("2. 等待浏览器自动打开")
        print("3. 开始使用股票分析功能")
        print("\n🔧 管理命令:")
        print("- 停止服务: pkill -f streamlit")
        print("- 查看日志: tail -f logs/app_launcher.log")
        print("- 检查状态: python verify_macos_app.py")
    else:
        print("😞 macOS应用程序存在问题")
        print("\n🛠️  故障排除:")
        print("1. 重新创建应用程序: python create_macos_app.py")
        print("2. 手动启动测试: ./quick_start.py")
        print("3. 检查环境: conda activate tradingagents-cn")

if __name__ == "__main__":
    main()
