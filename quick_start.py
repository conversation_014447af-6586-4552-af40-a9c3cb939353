#!/usr/bin/env python3
"""
TradingAgents-CN 快速启动脚本 (macOS专用)
简化版一键启动程序
"""

import os
import sys
import time
import subprocess
import webbrowser
from pathlib import Path
from datetime import datetime

class QuickLauncher:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.conda_env = "tradingagents-cn"
        self.web_port = 8501
        self.process = None
        
    def print_banner(self):
        """显示启动横幅"""
        print("\n" + "="*60)
        print("🚀 TradingAgents-CN 快速启动器 (macOS)")
        print("🤖 智能交易分析系统")
        print("="*60)
        print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 项目目录: {self.project_root}")
        print("="*60 + "\n")
    
    def check_environment(self):
        """检查运行环境"""
        print("🔍 检查运行环境...")
        
        # 检查conda
        try:
            result = subprocess.run(["conda", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Conda: {result.stdout.strip()}")
            else:
                print("❌ Conda未安装或不可用")
                return False
        except FileNotFoundError:
            print("❌ 未找到conda命令")
            return False
        
        # 检查conda环境
        try:
            result = subprocess.run(["conda", "env", "list"], capture_output=True, text=True)
            if self.conda_env in result.stdout:
                print(f"✅ Conda环境: {self.conda_env}")
            else:
                print(f"❌ Conda环境 '{self.conda_env}' 不存在")
                return False
        except:
            print("❌ 无法检查conda环境")
            return False
        
        # 检查关键文件
        key_files = ["start_web.py", ".env", "tradingagents"]
        for file in key_files:
            file_path = self.project_root / file
            if file_path.exists():
                print(f"✅ 关键文件: {file}")
            else:
                print(f"❌ 缺失文件: {file}")
                return False
        
        print("✅ 环境检查通过\n")
        return True
    
    def check_port(self):
        """检查端口是否可用"""
        print(f"🔌 检查端口 {self.web_port}...")
        
        try:
            result = subprocess.run(
                ["lsof", "-i", f":{self.web_port}"], 
                capture_output=True, 
                text=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                print(f"⚠️  端口 {self.web_port} 被占用，尝试清理...")
                # 尝试停止占用进程
                subprocess.run(["pkill", "-f", "streamlit"], capture_output=True)
                subprocess.run(["pkill", "-f", "start_web.py"], capture_output=True)
                time.sleep(2)
                
                # 再次检查
                result = subprocess.run(
                    ["lsof", "-i", f":{self.web_port}"], 
                    capture_output=True, 
                    text=True
                )
                
                if result.returncode == 0 and result.stdout.strip():
                    print(f"❌ 无法释放端口 {self.web_port}")
                    return False
                else:
                    print(f"✅ 端口 {self.web_port} 已释放")
            else:
                print(f"✅ 端口 {self.web_port} 可用")
            
            return True
            
        except FileNotFoundError:
            print("⚠️  无法检查端口状态（lsof不可用）")
            return True
    
    def start_service(self):
        """启动服务"""
        print("🚀 启动TradingAgents-CN服务...")
        
        try:
            # 切换到项目目录
            os.chdir(self.project_root)
            
            # 构建启动命令
            start_script = f"""
            #!/bin/bash
            set -e
            
            # 获取conda路径
            CONDA_BASE=$(conda info --base)
            source "$CONDA_BASE/etc/profile.d/conda.sh"
            
            # 激活环境
            conda activate {self.conda_env}
            
            # 启动streamlit
            streamlit run start_web.py \\
                --server.port {self.web_port} \\
                --server.address 0.0.0.0 \\
                --server.headless true \\
                --server.runOnSave false
            """
            
            # 创建临时启动脚本
            script_path = self.project_root / "temp_start.sh"
            with open(script_path, 'w') as f:
                f.write(start_script)
            script_path.chmod(0o755)
            
            print("🔧 正在启动Streamlit服务...")
            
            # 启动进程
            self.process = subprocess.Popen(
                ["bash", str(script_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # 等待服务启动
            print("⏳ 等待服务启动...")
            max_wait = 30
            for i in range(max_wait):
                try:
                    # 检查进程是否还在运行
                    if self.process.poll() is not None:
                        print("❌ 服务进程意外退出")
                        # 显示错误输出
                        if self.process.stdout:
                            output = self.process.stdout.read()
                            if output:
                                print(f"错误输出: {output}")
                        return False
                    
                    # 检查服务是否可访问
                    result = subprocess.run(
                        ["curl", "-s", "-o", "/dev/null", "-w", "%{http_code}", 
                         f"http://localhost:{self.web_port}"],
                        capture_output=True,
                        text=True,
                        timeout=2
                    )
                    
                    if result.returncode == 0 and result.stdout == "200":
                        print("✅ 服务启动成功!")
                        return True
                        
                except subprocess.TimeoutExpired:
                    pass
                except:
                    pass
                
                time.sleep(1)
                if i % 5 == 0 and i > 0:
                    print(f"⏳ 等待中... ({i}/{max_wait})")
            
            print("❌ 服务启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
        finally:
            # 清理临时脚本
            script_path = self.project_root / "temp_start.sh"
            if script_path.exists():
                script_path.unlink()
    
    def open_browser(self):
        """打开浏览器"""
        print("🌐 正在打开浏览器...")
        time.sleep(2)  # 等待服务完全就绪
        
        try:
            webbrowser.open(f"http://localhost:{self.web_port}")
            print(f"✅ 浏览器已打开: http://localhost:{self.web_port}")
        except Exception as e:
            print(f"⚠️  无法自动打开浏览器: {e}")
            print(f"请手动访问: http://localhost:{self.web_port}")
    
    def show_success_info(self):
        """显示成功信息"""
        print("\n" + "="*60)
        print("🎉 TradingAgents-CN 启动成功!")
        print("="*60)
        print(f"🌐 Web界面地址: http://localhost:{self.web_port}")
        print("📊 功能特性:")
        print("  • 多AI模型智能分析")
        print("  • 实时股票数据获取")
        print("  • 技术指标计算")
        print("  • 投资建议生成")
        print("\n💡 使用提示:")
        print("  • 按 Ctrl+C 停止服务")
        print("  • 服务会在后台持续运行")
        print("  • 关闭终端不会停止服务")
        print("="*60 + "\n")
    
    def stop_service(self):
        """停止服务"""
        print("\n🛑 正在停止服务...")
        
        if self.process and self.process.poll() is None:
            self.process.terminate()
            try:
                self.process.wait(timeout=5)
                print("✅ 服务已停止")
            except subprocess.TimeoutExpired:
                self.process.kill()
                print("⚠️  强制停止服务")
        
        # 额外清理
        subprocess.run(["pkill", "-f", "streamlit"], capture_output=True)
        subprocess.run(["pkill", "-f", "start_web.py"], capture_output=True)
        
        print("✅ 清理完成")
    
    def run(self):
        """运行启动器"""
        try:
            self.print_banner()
            
            if not self.check_environment():
                print("❌ 环境检查失败，无法启动")
                return False
            
            if not self.check_port():
                print("❌ 端口检查失败，无法启动")
                return False
            
            if not self.start_service():
                print("❌ 服务启动失败")
                return False
            
            self.open_browser()
            self.show_success_info()
            
            # 等待用户中断
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            print(f"❌ 运行过程中发生错误: {e}")
            return False
        finally:
            self.stop_service()

def main():
    """主函数"""
    launcher = QuickLauncher()
    success = launcher.run()
    
    if success:
        print("👋 感谢使用TradingAgents-CN!")
    else:
        print("😞 启动失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
