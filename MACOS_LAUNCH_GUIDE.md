# 🍎 TradingAgents-CN macOS启动指南

## 🎯 一键启动选项

您现在有**4种方式**启动TradingAgents-CN，选择最适合您的方式：

---

## 🚀 方式1: 快速启动脚本 (推荐)

**最简单的启动方式，适合日常使用**

### 启动命令：
```bash
# 在项目目录下运行
./quick_start.py
```

### 特点：
- ✅ 自动环境检查
- ✅ 自动端口检测
- ✅ 自动打开浏览器
- ✅ 友好的用户界面
- ✅ 按Ctrl+C优雅退出

---

## 🖥️ 方式2: 图形界面启动器

**带GUI的启动器，适合不熟悉命令行的用户**

### 启动命令：
```bash
# 启动图形界面
python launch_tradingagents.py --gui

# 或者直接运行（默认启动GUI）
python launch_tradingagents.py
```

### 特点：
- ✅ 图形化操作界面
- ✅ 实时日志显示
- ✅ 一键启动/停止
- ✅ 系统测试功能
- ✅ 浏览器快捷打开

---

## 📱 方式3: macOS应用程序 (最便捷)

**创建真正的macOS应用程序，可双击启动**

### 创建应用程序：
```bash
# 运行应用程序创建器
python create_macos_app.py
```

### 使用方法：
1. 运行创建脚本后，会在桌面生成 `TradingAgents-CN.app`
2. 双击桌面图标即可启动
3. 可以拖拽到Dock栏固定
4. 像使用其他macOS应用一样使用

### 特点：
- ✅ 双击启动，无需命令行
- ✅ 集成到macOS系统
- ✅ 可添加到Dock和Launchpad
- ✅ 系统通知支持

---

## 🔧 方式4: 传统脚本启动

**传统的shell脚本启动方式**

### 启动命令：
```bash
# 启动服务
./start_tradingagents.sh start

# 查看状态
./start_tradingagents.sh status

# 停止服务
./start_tradingagents.sh stop

# 重启服务
./start_tradingagents.sh restart

# 运行测试
./start_tradingagents.sh test

# 查看日志
./start_tradingagents.sh logs
```

### 特点：
- ✅ 完整的命令行控制
- ✅ 详细的日志记录
- ✅ 多种操作选项
- ✅ 适合高级用户

---

## 🎯 推荐使用顺序

### 新手用户：
1. **快速启动脚本** (`./quick_start.py`) - 最简单
2. **图形界面启动器** (`python launch_tradingagents.py --gui`) - 可视化
3. **macOS应用程序** (`python create_macos_app.py`) - 最便捷

### 高级用户：
1. **传统脚本启动** (`./start_tradingagents.sh`) - 完全控制
2. **快速启动脚本** (`./quick_start.py`) - 日常使用

---

## 🔍 启动前检查

确保以下条件满足：

### 必需条件：
- ✅ macOS 10.15+ 系统
- ✅ Anaconda/Miniconda已安装
- ✅ `tradingagents-cn` conda环境已创建
- ✅ 项目依赖已安装完成

### 检查命令：
```bash
# 检查conda
conda --version

# 检查环境
conda env list | grep tradingagents-cn

# 检查端口
lsof -i :8501
```

---

## 🌐 访问地址

启动成功后，通过以下地址访问：

- **本地访问**: http://localhost:8501
- **局域网访问**: http://你的IP地址:8501

---

## 🆘 故障排除

### 常见问题：

#### 1. 端口被占用
```bash
# 查看占用进程
lsof -i :8501

# 停止占用进程
pkill -f streamlit
```

#### 2. Conda环境问题
```bash
# 重新激活环境
conda activate tradingagents-cn

# 检查Python版本
python --version
```

#### 3. 权限问题
```bash
# 设置执行权限
chmod +x quick_start.py
chmod +x start_tradingagents.sh
chmod +x launch_tradingagents.py
```

#### 4. 依赖问题
```bash
# 重新安装依赖
conda activate tradingagents-cn
pip install -r requirements.txt
```

---

## 📊 性能优化

### 系统资源：
- **内存使用**: ~1.5GB
- **CPU使用**: ~10-20%
- **启动时间**: ~30秒

### 优化建议：
1. 关闭不必要的应用程序
2. 确保有足够的磁盘空间
3. 使用SSD硬盘可提升性能

---

## 🎉 启动成功标志

看到以下信息表示启动成功：

```
✅ Streamlit服务启动成功!
🌐 Web界面地址: http://localhost:8501
🎉 TradingAgents-CN 启动成功!
```

---

## 💡 使用技巧

1. **快速重启**: 使用 `./start_tradingagents.sh restart`
2. **后台运行**: 启动后可以关闭终端，服务继续运行
3. **多标签页**: 可以在浏览器中打开多个标签页
4. **移动设备**: 可以通过手机/平板访问（同一网络下）

---

## 🔗 相关文档

- **快速入门**: `QUICKSTART_CN.md`
- **部署状态**: `DEPLOYMENT_STATUS.md`
- **高级部署**: `ADVANCED_DEPLOYMENT.md`
- **API配置**: `.env` 文件

---

**🎊 现在选择您喜欢的启动方式，开始您的智能交易之旅吧！**
