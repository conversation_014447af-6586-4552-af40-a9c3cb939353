#!/usr/bin/env python3
"""
TradingAgents-CN 进一步部署自动化脚本
自动化完成高级功能的部署和配置
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

class AdvancedDeployer:
    def __init__(self):
        self.project_root = Path.cwd()
        self.config_dir = self.project_root / "config"
        self.logs_dir = self.project_root / "logs"
        self.data_dir = self.project_root / "data"
        self.scripts_dir = self.project_root / "scripts"
        
    def print_step(self, step, message):
        """打印部署步骤"""
        print(f"\n{'='*60}")
        print(f"步骤 {step}: {message}")
        print(f"{'='*60}")
    
    def run_command(self, command, check=True):
        """运行系统命令"""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if check and result.returncode != 0:
                print(f"❌ 命令执行失败: {command}")
                print(f"错误信息: {result.stderr}")
                return False
            return result.stdout.strip()
        except Exception as e:
            print(f"❌ 命令执行异常: {e}")
            return False
    
    def create_directories(self):
        """创建必要的目录结构"""
        self.print_step(1, "创建目录结构")
        
        directories = [
            self.config_dir,
            self.logs_dir,
            self.data_dir,
            self.scripts_dir,
            self.project_root / "strategies" / "custom",
            self.project_root / "backup",
            self.project_root / "monitoring"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
    
    def setup_databases(self):
        """设置数据库服务"""
        self.print_step(2, "配置数据库服务")
        
        # 检查Redis
        redis_status = self.run_command("redis-cli ping", check=False)
        if redis_status == "PONG":
            print("✅ Redis 服务正在运行")
        else:
            print("⚠️  Redis 服务未运行，尝试启动...")
            if sys.platform == "darwin":  # macOS
                self.run_command("brew services start redis", check=False)
            else:
                print("请手动启动Redis服务")
        
        # 检查MongoDB
        mongo_status = self.run_command('mongosh --eval "db.runCommand(\'ping\')" --quiet', check=False)
        if "ok" in str(mongo_status):
            print("✅ MongoDB 服务正在运行")
        else:
            print("⚠️  MongoDB 服务未运行，尝试启动...")
            if sys.platform == "darwin":  # macOS
                self.run_command("brew services start mongodb-community", check=False)
            else:
                print("请手动启动MongoDB服务")
    
    def create_config_files(self):
        """创建配置文件"""
        self.print_step(3, "创建配置文件")
        
        # 实时数据配置
        realtime_config = """
realtime:
  enabled: true
  sources:
    - akshare
    - tushare
  update_interval: 60  # 秒
  symbols:
    - "000001.SZ"  # 平安银行
    - "000002.SZ"  # 万科A
    - "600000.SH"  # 浦发银行
    - "600036.SH"  # 招商银行
  market_hours:
    start: "09:30"
    end: "15:00"
    timezone: "Asia/Shanghai"
"""
        
        with open(self.config_dir / "realtime_config.yaml", "w", encoding="utf-8") as f:
            f.write(realtime_config)
        print("✅ 创建实时数据配置文件")
        
        # 策略配置
        strategy_config = """
strategies:
  enabled:
    - "ma_crossover"
    - "rsi_reversal"
    - "bollinger_bands"
  risk_management:
    max_position_size: 0.1
    stop_loss: 0.05
    take_profit: 0.15
    max_drawdown: 0.20
  backtesting:
    start_date: "2023-01-01"
    end_date: "2024-12-31"
    initial_cash: 100000
    commission: 0.001
"""
        
        with open(self.config_dir / "strategy_config.yaml", "w", encoding="utf-8") as f:
            f.write(strategy_config)
        print("✅ 创建策略配置文件")
        
        # 监控配置
        monitoring_config = """
monitoring:
  enabled: true
  metrics:
    - system_resources
    - api_response_time
    - database_performance
    - trading_performance
  alerts:
    email: false
    webhook: false
  retention_days: 30
"""
        
        with open(self.config_dir / "monitoring_config.yaml", "w", encoding="utf-8") as f:
            f.write(monitoring_config)
        print("✅ 创建监控配置文件")
    
    def create_scripts(self):
        """创建管理脚本"""
        self.print_step(4, "创建管理脚本")
        
        # 备份脚本
        backup_script = f"""#!/bin/bash
# TradingAgents-CN 自动备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="{self.project_root}/backup"
PROJECT_DIR="{self.project_root}"

echo "开始备份 TradingAgents-CN 数据..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
echo "备份配置文件..."
tar -czf $BACKUP_DIR/config_$DATE.tar.gz -C $PROJECT_DIR config/ .env

# 备份数据目录
echo "备份数据目录..."
tar -czf $BACKUP_DIR/data_$DATE.tar.gz -C $PROJECT_DIR data/

# 备份MongoDB（如果运行）
if pgrep mongod > /dev/null; then
    echo "备份MongoDB数据..."
    mongodump --out $BACKUP_DIR/mongodb_$DATE
fi

# 备份Redis（如果运行）
if pgrep redis-server > /dev/null; then
    echo "备份Redis数据..."
    redis-cli --rdb $BACKUP_DIR/redis_$DATE.rdb
fi

# 清理旧备份（保留7天）
echo "清理旧备份文件..."
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "mongodb_*" -mtime +7 -exec rm -rf {{}} \\;
find $BACKUP_DIR -name "redis_*.rdb" -mtime +7 -delete

echo "备份完成！备份文件保存在: $BACKUP_DIR"
"""
        
        backup_script_path = self.scripts_dir / "backup.sh"
        with open(backup_script_path, "w") as f:
            f.write(backup_script)
        backup_script_path.chmod(0o755)
        print("✅ 创建备份脚本")
        
        # 启动脚本
        start_script = f"""#!/bin/bash
# TradingAgents-CN 启动脚本

cd {self.project_root}

echo "启动 TradingAgents-CN 服务..."

# 激活conda环境
source $(conda info --base)/etc/profile.d/conda.sh
conda activate tradingagents-cn

# 检查环境
echo "检查Python环境..."
python --version

# 启动Web服务
echo "启动Streamlit Web界面..."
streamlit run start_web.py --server.port 8501 --server.address 0.0.0.0 &

echo "TradingAgents-CN 已启动！"
echo "Web界面: http://localhost:8501"
echo "使用 'pkill -f streamlit' 停止服务"
"""
        
        start_script_path = self.scripts_dir / "start.sh"
        with open(start_script_path, "w") as f:
            f.write(start_script)
        start_script_path.chmod(0o755)
        print("✅ 创建启动脚本")
        
        # 停止脚本
        stop_script = """#!/bin/bash
# TradingAgents-CN 停止脚本

echo "停止 TradingAgents-CN 服务..."

# 停止Streamlit
pkill -f streamlit
echo "✅ Streamlit 服务已停止"

# 停止其他相关进程
pkill -f "python.*tradingagents"
echo "✅ 相关Python进程已停止"

echo "TradingAgents-CN 服务已完全停止"
"""
        
        stop_script_path = self.scripts_dir / "stop.sh"
        with open(stop_script_path, "w") as f:
            f.write(stop_script)
        stop_script_path.chmod(0o755)
        print("✅ 创建停止脚本")
    
    def create_monitoring_script(self):
        """创建监控脚本"""
        self.print_step(5, "创建监控脚本")
        
        monitoring_script = '''#!/usr/bin/env python3
"""
TradingAgents-CN 系统监控脚本
"""

import psutil
import time
import json
from datetime import datetime
from pathlib import Path

def get_system_metrics():
    """获取系统指标"""
    return {
        "timestamp": datetime.now().isoformat(),
        "cpu_percent": psutil.cpu_percent(interval=1),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_percent": psutil.disk_usage('/').percent,
        "network_io": dict(psutil.net_io_counters()._asdict()),
        "process_count": len(psutil.pids())
    }

def save_metrics(metrics, log_file):
    """保存指标到文件"""
    with open(log_file, "a") as f:
        f.write(json.dumps(metrics) + "\\n")

def main():
    log_file = Path("logs/system_metrics.jsonl")
    log_file.parent.mkdir(exist_ok=True)
    
    print("🔍 开始系统监控...")
    print("按 Ctrl+C 停止监控")
    
    try:
        while True:
            metrics = get_system_metrics()
            save_metrics(metrics, log_file)
            
            print(f"CPU: {metrics['cpu_percent']:.1f}% | "
                  f"内存: {metrics['memory_percent']:.1f}% | "
                  f"磁盘: {metrics['disk_percent']:.1f}%")
            
            time.sleep(60)  # 每分钟记录一次
            
    except KeyboardInterrupt:
        print("\\n监控已停止")

if __name__ == "__main__":
    main()
'''
        
        monitoring_script_path = self.scripts_dir / "monitor.py"
        with open(monitoring_script_path, "w") as f:
            f.write(monitoring_script)
        monitoring_script_path.chmod(0o755)
        print("✅ 创建监控脚本")
    
    def update_env_config(self):
        """更新环境配置"""
        self.print_step(6, "更新环境配置")
        
        env_file = self.project_root / ".env"
        if env_file.exists():
            with open(env_file, "r") as f:
                content = f.read()
            
            # 启用数据库
            if "MONGODB_ENABLED=false" in content:
                content = content.replace("MONGODB_ENABLED=false", "MONGODB_ENABLED=true")
                print("✅ 启用MongoDB")
            
            if "REDIS_ENABLED=false" in content:
                content = content.replace("REDIS_ENABLED=false", "REDIS_ENABLED=true")
                print("✅ 启用Redis")
            
            # 添加高级配置
            advanced_config = """

# ===== 高级配置 =====

# 实时数据配置
REALTIME_DATA_ENABLED=true
REALTIME_UPDATE_INTERVAL=60

# 策略引擎配置
STRATEGY_ENGINE_ENABLED=true
BACKTEST_ENABLED=true

# 监控配置
MONITORING_ENABLED=true
METRICS_RETENTION_DAYS=30

# 性能优化
CACHE_ENABLED=true
ASYNC_PROCESSING=true
MAX_WORKERS=4
"""
            
            if "# ===== 高级配置 =====" not in content:
                content += advanced_config
                print("✅ 添加高级配置")
            
            with open(env_file, "w") as f:
                f.write(content)
    
    def run_final_tests(self):
        """运行最终测试"""
        self.print_step(7, "运行系统测试")
        
        print("运行完整系统测试...")
        test_result = self.run_command("python test_system.py", check=False)
        if test_result:
            print("✅ 系统测试完成")
        
        print("运行API密钥测试...")
        api_test_result = self.run_command("python test_api_keys.py", check=False)
        if api_test_result:
            print("✅ API测试完成")
    
    def deploy(self):
        """执行完整部署"""
        print("🚀 开始 TradingAgents-CN 进一步部署")
        print(f"📅 部署时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            self.create_directories()
            self.setup_databases()
            self.create_config_files()
            self.create_scripts()
            self.create_monitoring_script()
            self.update_env_config()
            self.run_final_tests()
            
            print("\n" + "="*60)
            print("🎉 进一步部署完成！")
            print("="*60)
            print("📋 部署内容:")
            print("  ✅ 目录结构已创建")
            print("  ✅ 数据库服务已配置")
            print("  ✅ 配置文件已生成")
            print("  ✅ 管理脚本已创建")
            print("  ✅ 监控系统已部署")
            print("  ✅ 环境配置已更新")
            
            print("\n🚀 快速开始:")
            print(f"  启动服务: ./scripts/start.sh")
            print(f"  停止服务: ./scripts/stop.sh")
            print(f"  备份数据: ./scripts/backup.sh")
            print(f"  系统监控: python scripts/monitor.py")
            
            print("\n📖 更多信息请查看: ADVANCED_DEPLOYMENT.md")
            
        except Exception as e:
            print(f"\n❌ 部署过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

def main():
    deployer = AdvancedDeployer()
    deployer.deploy()

if __name__ == "__main__":
    main()
