#!/usr/bin/env python3
"""
测试新增的量化分析师和宏观经济分析师
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tradingagents.agents.analysts.quantitative_analyst import create_quantitative_analyst
from tradingagents.agents.analysts.macro_analyst import create_macro_analyst
from tradingagents.agents.toolkit import Toolkit
from tradingagents.agents.config_manager import ConfigManager
from tradingagents.utils.logging_init import get_logger

logger = get_logger("test")

def test_quantitative_analyst():
    """测试量化分析师"""
    print("=" * 50)
    print("测试量化分析师")
    print("=" * 50)
    
    try:
        # 创建配置和工具包
        config = ConfigManager()
        toolkit = Toolkit(config)
        
        # 创建LLM (使用简单的模拟)
        class MockLLM:
            def invoke(self, messages):
                class MockResult:
                    content = "这是一个模拟的量化分析报告。基于技术指标分析，该股票显示出积极的量化信号。"
                    tool_calls = []
                return MockResult()
        
        llm = MockLLM()
        
        # 创建量化分析师
        quantitative_analyst = create_quantitative_analyst(llm, toolkit)
        
        # 模拟状态
        state = {
            "messages": [],
            "company_of_interest": "000001",
            "trade_date": "2025-07-30"
        }
        
        # 执行分析
        result = quantitative_analyst(state)
        
        print("✅ 量化分析师执行成功")
        print(f"返回键: {list(result.keys())}")
        print(f"消息数量: {len(result.get('messages', []))}")
        print(f"量化报告存在: {'quantitative_report' in result}")
        
        if 'quantitative_report' in result:
            report = result['quantitative_report']
            print(f"报告长度: {len(report)}")
            print(f"报告预览: {report[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 量化分析师测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_macro_analyst():
    """测试宏观经济分析师"""
    print("=" * 50)
    print("测试宏观经济分析师")
    print("=" * 50)
    
    try:
        # 创建配置和工具包
        config = ConfigManager()
        toolkit = Toolkit(config)
        
        # 创建LLM (使用简单的模拟)
        class MockLLM:
            def invoke(self, messages):
                class MockResult:
                    content = "这是一个模拟的宏观经济分析报告。当前宏观环境对该股票呈现中性偏积极的影响。"
                    tool_calls = []
                return MockResult()
        
        llm = MockLLM()
        
        # 创建宏观分析师
        macro_analyst = create_macro_analyst(llm, toolkit)
        
        # 模拟状态
        state = {
            "messages": [],
            "company_of_interest": "000001",
            "trade_date": "2025-07-30"
        }
        
        # 执行分析
        result = macro_analyst(state)
        
        print("✅ 宏观分析师执行成功")
        print(f"返回键: {list(result.keys())}")
        print(f"消息数量: {len(result.get('messages', []))}")
        print(f"宏观报告存在: {'macro_report' in result}")
        
        if 'macro_report' in result:
            report = result['macro_report']
            print(f"报告长度: {len(report)}")
            print(f"报告预览: {report[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 宏观分析师测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试新增分析师...")
    
    # 测试量化分析师
    quant_success = test_quantitative_analyst()
    
    print()
    
    # 测试宏观分析师
    macro_success = test_macro_analyst()
    
    print()
    print("=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"量化分析师: {'✅ 通过' if quant_success else '❌ 失败'}")
    print(f"宏观分析师: {'✅ 通过' if macro_success else '❌ 失败'}")
    
    if quant_success and macro_success:
        print("🎉 所有测试通过！新分析师工作正常。")
        return 0
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return 1

if __name__ == "__main__":
    exit(main())
