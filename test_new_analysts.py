#!/usr/bin/env python3
"""
测试新添加的量化分析师和宏观经济分析师
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

def test_new_analysts():
    """测试新的分析师功能"""
    print("🧪 测试新添加的量化分析师和宏观经济分析师")
    print("=" * 60)
    
    try:
        # 配置阿里百炼
        config = DEFAULT_CONFIG.copy()
        config["llm_provider"] = "dashscope"
        config["deep_think_llm"] = "qwen-plus"
        config["quick_think_llm"] = "qwen-turbo"
        config["online_tools"] = True
        
        print("✅ 配置创建成功")
        
        # 测试1: 只使用新分析师
        print("\n📊 测试1: 只使用量化分析师")
        try:
            ta_quant = TradingAgentsGraph(
                selected_analysts=["quantitative"],
                debug=True,
                config=config
            )
            print("✅ 量化分析师图形创建成功")
        except Exception as e:
            print(f"❌ 量化分析师创建失败: {e}")
        
        # 测试2: 只使用宏观经济分析师
        print("\n🌍 测试2: 只使用宏观经济分析师")
        try:
            ta_macro = TradingAgentsGraph(
                selected_analysts=["macro"],
                debug=True,
                config=config
            )
            print("✅ 宏观经济分析师图形创建成功")
        except Exception as e:
            print(f"❌ 宏观经济分析师创建失败: {e}")
        
        # 测试3: 使用所有分析师（包括新的）
        print("\n🎯 测试3: 使用所有分析师")
        try:
            ta_all = TradingAgentsGraph(
                selected_analysts=["market", "fundamentals", "news", "social", "quantitative", "macro"],
                debug=True,
                config=config
            )
            print("✅ 完整分析师团队创建成功")
            
            # 进行一次简单的分析测试
            print("\n🔍 执行简单分析测试...")
            state, decision = ta_all.propagate("000858", "2025-01-17")
            
            print(f"📈 分析完成!")
            print(f"投资建议: {decision.get('action', 'N/A')}")
            print(f"置信度: {decision.get('confidence', 'N/A')}")
            
            # 检查新分析师的报告
            if hasattr(state, 'get'):
                quant_report = state.get('quantitative_report', '')
                macro_report = state.get('macro_report', '')
                
                print(f"\n📊 量化分析报告长度: {len(quant_report)}")
                print(f"🌍 宏观经济分析报告长度: {len(macro_report)}")
                
                if quant_report:
                    print("✅ 量化分析师成功生成报告")
                else:
                    print("⚠️ 量化分析师未生成报告")
                    
                if macro_report:
                    print("✅ 宏观经济分析师成功生成报告")
                else:
                    print("⚠️ 宏观经济分析师未生成报告")
            
        except Exception as e:
            print(f"❌ 完整分析测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_new_analysts()
