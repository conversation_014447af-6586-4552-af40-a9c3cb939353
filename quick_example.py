#!/usr/bin/env python3
"""
TradingAgents-CN 快速示例
演示如何使用系统获取和分析股票数据
"""

import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def get_stock_data():
    """获取股票数据"""
    print("🔍 正在获取股票数据...")
    
    try:
        # 获取上证指数数据
        print("  - 获取上证指数数据...")
        sh_data = ak.stock_zh_index_daily(symbol='sh000001')
        
        # 获取深证成指数据
        print("  - 获取深证成指数据...")
        sz_data = ak.stock_zh_index_daily(symbol='sz399001')
        
        # 获取创业板指数据
        print("  - 获取创业板指数据...")
        cy_data = ak.stock_zh_index_daily(symbol='sz399006')
        
        return {
            '上证指数': sh_data,
            '深证成指': sz_data,
            '创业板指': cy_data
        }
        
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        return None

def analyze_data(data_dict):
    """分析数据"""
    print("\n📊 正在分析数据...")
    
    analysis_results = {}
    
    for name, data in data_dict.items():
        print(f"\n=== {name} 分析 ===")
        
        # 获取最近30天数据
        recent_data = data.tail(30).copy()
        
        # 基本统计
        latest_close = recent_data.iloc[-1]['close']
        max_price = recent_data['high'].max()
        min_price = recent_data['low'].min()
        avg_price = recent_data['close'].mean()
        
        # 计算技术指标
        recent_data['MA5'] = recent_data['close'].rolling(window=5).mean()
        recent_data['MA10'] = recent_data['close'].rolling(window=10).mean()
        recent_data['MA20'] = recent_data['close'].rolling(window=20).mean()
        
        # 计算涨跌幅
        recent_data['pct_change'] = recent_data['close'].pct_change() * 100
        
        # 计算波动率
        volatility = recent_data['pct_change'].std()
        
        # 趋势判断
        ma5_trend = "上升" if recent_data['MA5'].iloc[-1] > recent_data['MA5'].iloc[-5] else "下降"
        ma10_trend = "上升" if recent_data['MA10'].iloc[-1] > recent_data['MA10'].iloc[-10] else "下降"
        
        # 金叉死叉判断
        current_ma5 = recent_data['MA5'].iloc[-1]
        current_ma10 = recent_data['MA10'].iloc[-1]
        prev_ma5 = recent_data['MA5'].iloc[-2]
        prev_ma10 = recent_data['MA10'].iloc[-2]
        
        cross_signal = "无信号"
        if current_ma5 > current_ma10 and prev_ma5 <= prev_ma10:
            cross_signal = "金叉 (买入信号)"
        elif current_ma5 < current_ma10 and prev_ma5 >= prev_ma10:
            cross_signal = "死叉 (卖出信号)"
        
        # 输出分析结果
        print(f"📈 最新收盘价: {latest_close:.2f}")
        print(f"📊 30天最高价: {max_price:.2f}")
        print(f"📉 30天最低价: {min_price:.2f}")
        print(f"📊 30天平均价: {avg_price:.2f}")
        print(f"📈 5日均线: {current_ma5:.2f} ({ma5_trend})")
        print(f"📈 10日均线: {current_ma10:.2f} ({ma10_trend})")
        print(f"⚡ 均线信号: {cross_signal}")
        print(f"📊 波动率: {volatility:.2f}%")
        
        # 简单评级
        score = 0
        if latest_close > avg_price:
            score += 1
        if ma5_trend == "上升":
            score += 1
        if ma10_trend == "上升":
            score += 1
        if cross_signal == "金叉 (买入信号)":
            score += 2
        elif cross_signal == "死叉 (卖出信号)":
            score -= 2
        
        if score >= 3:
            rating = "强烈看好 🚀"
        elif score >= 1:
            rating = "谨慎看好 📈"
        elif score <= -1:
            rating = "谨慎看空 📉"
        else:
            rating = "中性观望 ➡️"
        
        print(f"🎯 综合评级: {rating}")
        
        analysis_results[name] = {
            'data': recent_data,
            'latest_close': latest_close,
            'rating': rating,
            'score': score,
            'volatility': volatility,
            'cross_signal': cross_signal
        }
    
    return analysis_results

def generate_report(analysis_results):
    """生成分析报告"""
    print("\n" + "="*60)
    print("📋 TradingAgents-CN 市场分析报告")
    print("="*60)
    print(f"📅 报告时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 分析周期: 最近30个交易日")
    
    print("\n🏆 指数排行榜:")
    sorted_results = sorted(analysis_results.items(), key=lambda x: x[1]['score'], reverse=True)
    
    for i, (name, result) in enumerate(sorted_results, 1):
        print(f"{i}. {name}: {result['latest_close']:.2f} - {result['rating']}")
    
    print("\n⚠️  风险提示:")
    high_vol_indices = [name for name, result in analysis_results.items() if result['volatility'] > 2.0]
    if high_vol_indices:
        print(f"   高波动率指数: {', '.join(high_vol_indices)}")
    else:
        print("   当前市场波动率相对稳定")
    
    print("\n📡 交易信号:")
    for name, result in analysis_results.items():
        if result['cross_signal'] != "无信号":
            print(f"   {name}: {result['cross_signal']}")
    
    print("\n💡 投资建议:")
    best_index = sorted_results[0]
    worst_index = sorted_results[-1]
    
    print(f"   🎯 关注重点: {best_index[0]} (评分: {best_index[1]['score']})")
    print(f"   ⚠️  谨慎对待: {worst_index[0]} (评分: {worst_index[1]['score']})")
    
    print("\n" + "="*60)
    print("📝 免责声明: 本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。")
    print("="*60)

def main():
    """主函数"""
    print("🎉 欢迎使用 TradingAgents-CN!")
    print("🤖 正在为您分析中国股市主要指数...")
    
    # 获取数据
    data_dict = get_stock_data()
    if not data_dict:
        print("❌ 无法获取数据，程序退出")
        return
    
    # 分析数据
    analysis_results = analyze_data(data_dict)
    
    # 生成报告
    generate_report(analysis_results)
    
    print("\n🎊 分析完成！感谢使用 TradingAgents-CN!")
    print("💡 提示: 您可以修改此脚本来分析特定股票或添加更多技术指标")

if __name__ == "__main__":
    main()
